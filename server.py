from flask import Flask, render_template, request, send_file, session, redirect, url_for
from docx import Document
import locale
from datetime import datetime
import os
import base64
import json
import time
import threading
import queue
import subprocess
import platform
import sys
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from arabic_number_converter import convert_number_to_arabic_text, format_number_with_currency
import qrcode
import uuid
from io import BytesIO
from qr_config import get_contract_qr_url, QR_SETTINGS, WORD_QR_SIZE, print_qr_debug

# استيراد Firebase Manager
from firebase_manager import (
    load_computers, save_computers, load_serial_number, save_serial_number,
    load_admin_config, save_admin_config, save_contract_data,
    load_saved_contract, delete_saved_contract, increment_user_contracts,
    check_permission, check_if_serial_exists, update_user_permissions,
    get_finalized_contracts_by_year, move_contracts_to_annual_archive,
    delete_finalized_contracts_by_year, get_available_years, migrate_permissions,
    update_computer_password, get_computer_password, cleanup_old_permissions
)

# ضبط اللغة العربية
try:
    locale.setlocale(locale.LC_TIME, 'ar_IQ.UTF-8')  # العراق
except locale.Error:
    # في حالة عدم توفر اللغة العربية في النظام
    try:
        locale.setlocale(locale.LC_TIME, 'C.UTF-8')
    except locale.Error:
        pass  # استخدام الإعداد الافتراضي

# استخدام الوحدة الجديدة لتحويل الأرقام إلى نص عربي فصيح
def number_to_arabic_text(number, currency_type):
    """
    تحويل الرقم إلى نص عربي فصيح مع مراعاة قواعد العدد والمعدود
    هذه دالة مُحدثة تستخدم الوحدة المحسنة
    """
    try:
        return convert_number_to_arabic_text(number, currency_type)
    except (ValueError, TypeError):
        # في حالة حدوث خطأ، إرجاع نص افتراضي
        if currency_type == "USD":
            return "صفر دولار أمريكي"
        else:  # IQD
            return "صفر دينار عراقي"

now = datetime.now()

UPLOAD_FOLDER = 'uploads'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

app = Flask(__name__)
app.secret_key = 'car_contract_system_2025'  # للجلسات

# ===== نظام تنظيف الذاكرة بعد كل طلب =====

def cleanup_memory_after_request():
    """تنظيف شامل للذاكرة بعد كل طلب لمنع التراكم"""
    try:
        # فحص استهلاك الذاكرة الحالي
        memory_mb, cpu_percent = get_system_stats()

        # تنظيف فوري إذا تجاوزت الذاكرة 100 MiB
        if memory_mb > 100:
            print(f"⚠️ الذاكرة تجاوزت 100 MiB ({memory_mb:.1f} MB) - بدء تنظيف فوري...")

            # تنظيف Gradio Client فوراً
            gradio_manager.cleanup()

            # تنظيف خاص بـ Gradio
            cleanup_gradio_specific_memory()

            # تنظيف الملفات المؤقتة
            cleanup_temp_files()

            # تنظيف session data
            cleanup_session_data()

            # تنظيف Flask context
            cleanup_flask_context()

            # تنظيف الذاكرة بقوة - أربع مرات للتأكد
            gc.collect()
            gc.collect()
            gc.collect()
            gc.collect()

            # فحص الذاكرة بعد التنظيف
            memory_after, _ = get_system_stats()
            print(f"🧹 تم تنظيف الذاكرة: من {memory_mb:.1f} MB إلى {memory_after:.1f} MB")

        else:
            # تنظيف خفيف بعد كل طلب
            gc.collect()

        # تحديث نشاط الطلبات
        update_request_activity()

    except Exception as e:
        print(f"⚠️ خطأ في تنظيف الذاكرة بعد الطلب: {e}")

def cleanup_temp_files():
    """تنظيف الملفات المؤقتة"""
    try:
        import tempfile
        import shutil

        # تنظيف مجلد uploads
        if os.path.exists(UPLOAD_FOLDER):
            for filename in os.listdir(UPLOAD_FOLDER):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                try:
                    if os.path.isfile(file_path):
                        # حذف الملفات الأقدم من 10 دقائق
                        if time.time() - os.path.getctime(file_path) > 600:
                            os.remove(file_path)
                except Exception:
                    continue

        # تنظيف temp directory
        temp_dir = tempfile.gettempdir()
        for filename in os.listdir(temp_dir):
            if filename.startswith('tmp') or filename.startswith('gradio'):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) and time.time() - os.path.getctime(file_path) > 300:
                        os.remove(file_path)
                except Exception:
                    continue

    except Exception as e:
        print(f"⚠️ خطأ في تنظيف الملفات المؤقتة: {e}")

def cleanup_session_data():
    """تنظيف بيانات الجلسة"""
    try:
        from flask import has_request_context, session, g

        # تنظيف فقط إذا كنا في request context
        if has_request_context():
            # تنظيف البيانات المؤقتة في session
            temp_keys = [key for key in session.keys() if key.startswith('temp_') or key.startswith('cache_')]
            for key in temp_keys:
                session.pop(key, None)

            # تنظيف g object
            if hasattr(g, '_database'):
                g._database = None
            if hasattr(g, '_temp_data'):
                g._temp_data = None

    except Exception as e:
        # تجاهل أخطاء Flask context عند التشغيل خارج request
        if "Working outside of request context" not in str(e):
            print(f"⚠️ خطأ في تنظيف بيانات الجلسة: {e}")

def cleanup_flask_context():
    """تنظيف Flask context"""
    try:
        from flask import has_request_context, has_app_context

        # تنظيف فقط إذا كنا في context مناسب
        if has_request_context() or has_app_context():
            # تنظيف متغيرات محلية فقط
            import gc
            gc.collect()

    except Exception as e:
        # تجاهل أخطاء Flask context
        if "Working outside" not in str(e) and "has no setter" not in str(e):
            print(f"⚠️ خطأ في تنظيف Flask context: {e}")

@app.before_request
def before_request_memory_check():
    """فحص الذاكرة قبل كل طلب"""
    try:
        memory_mb, _ = get_system_stats()
        if memory_mb > 150:  # إذا تجاوزت 150 MB قبل الطلب
            print(f"⚠️ الذاكرة عالية قبل الطلب: {memory_mb:.1f} MB - تنظيف فوري...")
            cleanup_memory_after_request()
    except Exception as e:
        print(f"⚠️ خطأ في فحص الذاكرة قبل الطلب: {e}")

@app.after_request
def after_request_cleanup(response):
    """تنظيف تلقائي للذاكرة بعد كل طلب"""
    cleanup_memory_after_request()
    return response

def force_memory_cleanup_if_needed():
    """تنظيف فوري للذاكرة إذا تجاوزت الحد المسموح"""
    try:
        memory_mb, _ = get_system_stats()
        if memory_mb > 80:  # حد أقل للتنظيف الاستباقي
            print(f"🧹 تنظيف استباقي للذاكرة: {memory_mb:.1f} MB")

            # تنظيف فوري وشامل
            gradio_manager.cleanup()
            cleanup_gradio_specific_memory()
            cleanup_temp_files()
            cleanup_session_data()

            # تنظيف قوي للذاكرة
            for _ in range(5):
                gc.collect()

            memory_after, _ = get_system_stats()
            print(f"✅ تم التنظيف الاستباقي: من {memory_mb:.1f} MB إلى {memory_after:.1f} MB")

    except Exception as e:
        print(f"⚠️ خطأ في التنظيف الاستباقي: {e}")

def aggressive_memory_cleanup():
    """تنظيف قوي جداً للذاكرة في الحالات الطارئة"""
    try:
        print("🚨 بدء تنظيف قوي للذاكرة...")

        # إغلاق جميع اتصالات Gradio
        gradio_manager.cleanup()

        # تنظيف خاص بـ Gradio
        cleanup_gradio_specific_memory()

        # تنظيف جميع الملفات المؤقتة
        cleanup_temp_files()

        # تنظيف session data
        cleanup_session_data()

        # تنظيف Flask context
        cleanup_flask_context()

        # تنظيف Python modules cache
        import sys
        if hasattr(sys, '_clear_type_cache'):
            sys._clear_type_cache()

        # تنظيف قوي جداً للذاكرة - 10 مرات
        for i in range(10):
            gc.collect()

        memory_after, _ = get_system_stats()
        print(f"✅ تم التنظيف القوي - الذاكرة الحالية: {memory_after:.1f} MB")

    except Exception as e:
        print(f"⚠️ خطأ في التنظيف القوي: {e}")

# ترحيل الصلاحيات للحاسبات الموجودة
migrate_permissions()

# مجلد حفظ العقود المحفوظة (للتوافق مع الكود القديم)
SAVED_CONTRACTS_FOLDER = 'saved_contracts'
os.makedirs(SAVED_CONTRACTS_FOLDER, exist_ok=True)

# ===== نظام الطابور للرقم التسلسلي =====
# طابور للطلبات - يدعم أكثر من 100 حاسوب بنفس الوقت
serial_number_queue = queue.Queue()
serial_number_lock = threading.Lock()
serial_number_worker_running = False

# ===== نظام التنظيف المركزي =====
cleanup_queue = queue.Queue()
cleanup_worker_running = False
cleanup_lock = threading.Lock()

# ===== نظام مراقبة الذاكرة الدوري =====
memory_monitor_running = False
memory_monitor_lock = threading.Lock()

def memory_monitor_worker():
    """عامل مراقبة الذاكرة - يعمل كل 5 دقائق فقط عند الحاجة"""
    global memory_monitor_running

    with memory_monitor_lock:
        if memory_monitor_running:
            return  # تجنب تشغيل عدة مراقبات
        memory_monitor_running = True

    try:
        print("🔍 بدء مراقب الذاكرة الدوري")

        # انتظار 5 دقائق قبل أول فحص
        time.sleep(300)

        while True:
            try:
                should_cleanup, memory_mb, cpu_percent = should_cleanup_memory()

                if should_cleanup:
                    print(f"⚠️ تم اكتشاف حاجة لتنظيف الذاكرة - الذاكرة: {memory_mb:.1f} MB, CPU: {cpu_percent:.1f}%")
                    success = perform_memory_cleanup()
                    if success:
                        print("✅ تم تنظيف الذاكرة بنجاح")
                    else:
                        print("❌ فشل في تنظيف الذاكرة")
                else:
                    # طباعة حالة النظام بشكل دوري (كل 30 دقيقة)
                    current_time = time.time()
                    if int(current_time) % 1800 == 0:  # كل 30 دقيقة
                        print(f"📊 حالة النظام - الذاكرة: {memory_mb:.1f} MB, CPU: {cpu_percent:.1f}%")

                # انتظار 5 دقائق قبل الفحص التالي
                time.sleep(300)

            except Exception as e:
                print(f"❌ خطأ في مراقب الذاكرة: {e}")
                time.sleep(300)  # انتظار 5 دقائق حتى في حالة الخطأ

    except Exception as e:
        print(f"❌ خطأ في بدء مراقب الذاكرة: {e}")
    finally:
        with memory_monitor_lock:
            memory_monitor_running = False
        print("🔴 تم إيقاف مراقب الذاكرة")

def start_memory_monitor():
    """بدء مراقب الذاكرة إذا لم يكن يعمل"""
    global memory_monitor_running

    with memory_monitor_lock:
        if not memory_monitor_running:
            monitor_thread = threading.Thread(target=memory_monitor_worker, daemon=True)
            monitor_thread.start()
            print("🟢 تم بدء مراقب الذاكرة الدوري")

# ===== نظام Gradio Client لتحويل Word إلى PDF (تحميل كسول) =====
# تم إزالة الاستيراد التلقائي لتوفير الموارد
GRADIO_CLIENT_AVAILABLE = None  # سيتم فحصه عند الحاجة

from gradio_config import get_gradio_space_id, get_timeout, get_error_message, GRADIO_SETTINGS

# إعدادات Gradio
GRADIO_SPACE_ID = get_gradio_space_id()
GRADIO_TIMEOUT = get_timeout()

# ===== مدير Gradio محسن للموارد مع تنظيف تلقائي =====
import time
import threading
from typing import Optional
import psutil
import gc

# متغيرات عامة لتتبع النشاط
last_request_time = time.time()
request_count = 0
request_lock = threading.Lock()

class OptimizedGradioManager:
    def __init__(self):
        self.client = None
        self.last_used = 0
        self.cache_duration = 10  # 10 ثواني بدلاً من 5 دقائق
        self.lock = threading.Lock()
        self.space_id = GRADIO_SPACE_ID
        self._gradio_available = None
        self.cleanup_timer = None

    def _check_gradio_availability(self):
        """فحص توفر مكتبة gradio_client"""
        if self._gradio_available is None:
            try:
                import gradio_client
                self._gradio_available = True
                print("✅ تم العثور على مكتبة gradio_client")
            except ImportError:
                self._gradio_available = False
                print("❌ مكتبة gradio_client غير مثبتة. يرجى تثبيتها: pip install gradio_client")
        return self._gradio_available

    def _schedule_cleanup(self):
        """جدولة تنظيف Gradio Client بعد 10 ثواني مع تنظيف فوري للذاكرة"""
        if self.cleanup_timer:
            self.cleanup_timer.cancel()

        def delayed_cleanup():
            with self.lock:
                if self.client and time.time() - self.last_used >= 10:
                    try:
                        self.client.close()
                        print("🧹 تم إغلاق Gradio Client بعد 10 ثواني من عدم الاستخدام")
                    except:
                        pass
                    self.client = None

                    # تنظيف فوري للذاكرة بعد إغلاق Gradio Client
                    self._cleanup_gradio_memory()

        self.cleanup_timer = threading.Timer(10.0, delayed_cleanup)
        self.cleanup_timer.daemon = True
        self.cleanup_timer.start()

    def _cleanup_gradio_memory(self):
        """تنظيف فوري للذاكرة المستخدمة من قبل Gradio Client"""
        try:
            # تنظيف الذاكرة بقوة
            gc.collect()
            gc.collect()  # مرتين للتأكد من تحرير ذاكرة gradio_client

            # تنظيف إضافي للمتغيرات المحلية
            import sys
            if 'gradio_client' in sys.modules:
                # محاولة تنظيف cache الداخلي لـ gradio_client إن وجد
                try:
                    gradio_module = sys.modules['gradio_client']
                    if hasattr(gradio_module, '_client_cache'):
                        gradio_module._client_cache.clear()
                except:
                    pass

            print("🧹 تم تنظيف ذاكرة Gradio Client فوراً")
        except Exception as e:
            print(f"⚠️ خطأ في تنظيف ذاكرة Gradio: {e}")

    def get_client(self) -> Optional[object]:
        """الحصول على Gradio Client مع تحسين الموارد"""
        if not self._check_gradio_availability():
            return None

        with self.lock:
            current_time = time.time()

            # فحص صلاحية Cache
            if (self.client is None or
                current_time - self.last_used > self.cache_duration):

                try:
                    from gradio_client import Client
                    self.client = Client(self.space_id)
                    print("✅ تم تحميل Gradio Client عند الحاجة")
                except Exception as e:
                    print(f"❌ خطأ في تحميل Gradio Client: {e}")
                    self.client = None
                    return None

            self.last_used = current_time
            # جدولة التنظيف التلقائي
            self._schedule_cleanup()
            return self.client

    def convert_to_pdf(self, word_file_path: str):
        """تحويل Word إلى PDF مع إدارة محسنة للموارد وتنظيف فوري للذاكرة"""
        client = self.get_client()
        if not client:
            return None

        try:
            from gradio_client import handle_file
            result = client.predict(handle_file(word_file_path), api_name="/predict")

            # تنظيف فوري للذاكرة بعد اكتمال العملية
            print("🧹 تنظيف فوري للذاكرة بعد استخدام Gradio Client...")
            self._cleanup_gradio_memory()

            # جدولة التنظيف بعد اكتمال العملية
            self._schedule_cleanup()
            return result
        except Exception as e:
            print(f"❌ خطأ في تحويل PDF: {e}")
            # إعادة تعيين Client عند الخطأ وتنظيف الذاكرة
            self.client = None
            self._cleanup_gradio_memory()
            return None

    def cleanup(self):
        """تنظيف الموارد مع تنظيف فوري للذاكرة"""
        if self.cleanup_timer:
            self.cleanup_timer.cancel()

        with self.lock:
            if self.client:
                try:
                    self.client.close()
                    print("🧹 تم إغلاق Gradio Client يدوياً")
                except:
                    pass
                self.client = None

                # تنظيف فوري للذاكرة بعد الإغلاق
                self._cleanup_gradio_memory()

# إنشاء مثيل عام
gradio_manager = OptimizedGradioManager()

def cleanup_gradio_specific_memory():
    """تنظيف خاص بذاكرة Gradio Client"""
    try:
        import sys

        # تنظيف modules المتعلقة بـ gradio_client
        gradio_modules = [module for module in sys.modules.keys() if 'gradio' in module.lower()]

        for module_name in gradio_modules:
            try:
                module = sys.modules[module_name]

                # تنظيف cache إن وجد
                if hasattr(module, '_client_cache'):
                    module._client_cache.clear()
                if hasattr(module, 'cache'):
                    module.cache.clear()
                if hasattr(module, '_cache'):
                    module._cache.clear()

                # تنظيف connections إن وجدت
                if hasattr(module, '_connections'):
                    module._connections.clear()
                if hasattr(module, 'connections'):
                    module.connections.clear()

            except Exception:
                continue

        # تنظيف متغيرات محلية متعلقة بـ gradio
        import gc
        for obj in gc.get_objects():
            try:
                if hasattr(obj, '__module__') and obj.__module__ and 'gradio' in obj.__module__.lower():
                    if hasattr(obj, 'clear'):
                        obj.clear()
                    elif hasattr(obj, 'close'):
                        obj.close()
            except Exception:
                continue

        print("🧹 تم تنظيف ذاكرة Gradio Client بشكل خاص")

    except Exception as e:
        print(f"⚠️ خطأ في تنظيف ذاكرة Gradio الخاص: {e}")

# ===== نظام مراقبة الذاكرة والمعالج =====
def update_request_activity():
    """تحديث نشاط الطلبات مع مراقبة الذاكرة"""
    global last_request_time, request_count
    with request_lock:
        last_request_time = time.time()
        request_count += 1

        # فحص الذاكرة مع كل طلب
        try:
            memory_mb, _ = get_system_stats()
            if memory_mb > 120:  # تنظيف استباقي عند 120 MB
                force_memory_cleanup_if_needed()
            elif memory_mb > 200:  # تنظيف قوي عند 200 MB
                aggressive_memory_cleanup()
        except Exception:
            pass

def get_system_stats():
    """الحصول على إحصائيات النظام"""
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        return memory_mb, cpu_percent
    except:
        return 0, 0

def should_cleanup_memory():
    """تحديد ما إذا كان يجب تنظيف الذاكرة"""
    try:
        memory_mb, cpu_percent = get_system_stats()
        current_time = time.time()

        # فحص عدم وجود طلبات لمدة 5 دقائق
        time_since_last_request = current_time - last_request_time
        no_recent_requests = time_since_last_request >= 300  # 5 دقائق

        # فحص الذاكرة أكثر من 200 MB أو CPU أكثر من 10%
        high_memory = memory_mb > 200
        high_cpu = cpu_percent > 10

        return no_recent_requests and (high_memory or high_cpu), memory_mb, cpu_percent
    except:
        return False, 0, 0

def perform_memory_cleanup():
    """تنفيذ تنظيف الذاكرة مع تنظيف خاص لـ Gradio Client"""
    try:
        print("🧹 بدء تنظيف الذاكرة والموارد...")

        # تنظيف Gradio Client مع تنظيف فوري للذاكرة
        gradio_manager.cleanup()

        # تنظيف إضافي خاص بـ Gradio
        cleanup_gradio_specific_memory()

        # تنظيف العمليات المعلقة
        cleanup_hanging_processes()

        # تنظيف الملفات المؤقتة
        schedule_cleanup_task('uploads_folder')

        # تنظيف الذاكرة بقوة - ثلاث مرات للتأكد من تحرير ذاكرة Gradio
        gc.collect()
        gc.collect()
        gc.collect()  # ثلاث مرات للتأكد من تحرير ذاكرة gradio_client

        # إحصائيات بعد التنظيف
        memory_mb, cpu_percent = get_system_stats()
        print(f"✅ تم تنظيف الذاكرة - الاستخدام الحالي: {memory_mb:.1f} MB, CPU: {cpu_percent:.1f}%")

        return True
    except Exception as e:
        print(f"❌ خطأ في تنظيف الذاكرة: {e}")
        return False

def convert_word_to_pdf_via_gradio(word_path, output_pdf_path):
    """
    تحويل ملف Word إلى PDF باستخدام Gradio Client المحسن (نفس طريقة xx.html)
    """
    # تحديث نشاط الطلبات
    update_request_activity()

    max_retries = GRADIO_SETTINGS['max_retries']
    retry_delay = GRADIO_SETTINGS['retry_delay']

    for attempt in range(max_retries):
        try:
            print(f"🔄 بدء تحويل Word إلى PDF عبر Gradio Client المحسن (المحاولة {attempt + 1}/{max_retries}): {word_path}")

            # التحقق من وجود ملف Word
            if not os.path.exists(word_path):
                print(f"❌ {get_error_message('file_not_found')}: {word_path}")
                return False

            print(f"📤 استخدام Gradio Manager المحسن للموارد...")

            # استخدام المدير المحسن
            result = gradio_manager.convert_to_pdf(word_path)

            if result is None:
                print(f"❌ فشل في التحويل عبر Gradio Manager")
                if attempt < max_retries - 1:
                    print(f"⏳ انتظار {retry_delay} ثانية قبل المحاولة التالية...")
                    time.sleep(retry_delay)
                    continue
                else:
                    return False

            print(f"📥 تم استلام النتيجة من Gradio Space")
            print(f"🔍 نوع النتيجة: {type(result)}")

            # معالجة النتيجة (نفس منطق xx.html)
            if result and len(result) >= 1:
                pdf_file_info = result[0] if isinstance(result, (list, tuple)) else result

                print(f"📄 معلومات ملف PDF: {pdf_file_info}")

                # التحقق من وجود مسار الملف
                if hasattr(pdf_file_info, 'name') and pdf_file_info.name:
                    pdf_source_path = pdf_file_info.name
                elif isinstance(pdf_file_info, str):
                    pdf_source_path = pdf_file_info
                else:
                    print(f"❌ تنسيق غير متوقع لملف PDF: {pdf_file_info}")
                    if attempt < max_retries - 1:
                        print(f"⏳ إعادة المحاولة بعد {retry_delay} ثانية...")
                        time.sleep(retry_delay)
                        continue
                    return False

                # نسخ ملف PDF إلى المسار المطلوب
                if os.path.exists(pdf_source_path):
                    import shutil
                    shutil.copy2(pdf_source_path, output_pdf_path)

                    # التحقق من صحة ملف PDF
                    if os.path.exists(output_pdf_path) and os.path.getsize(output_pdf_path) > 0:
                        print(f"✅ تم تحويل Word إلى PDF بنجاح عبر Gradio Client: {output_pdf_path}")
                        return True
                    else:
                        print(f"❌ {get_error_message('empty_pdf')}")
                        if attempt < max_retries - 1:
                            print(f"⏳ إعادة المحاولة بعد {retry_delay} ثانية...")
                            time.sleep(retry_delay)
                            continue
                        return False
                else:
                    print(f"❌ ملف PDF المصدر غير موجود: {pdf_source_path}")
                    if attempt < max_retries - 1:
                        print(f"⏳ إعادة المحاولة بعد {retry_delay} ثانية...")
                        time.sleep(retry_delay)
                        continue
                    return False
            else:
                print(f"❌ {get_error_message('invalid_response')}")
                print(f"   النتيجة المستلمة: {result}")
                if attempt < max_retries - 1:
                    print(f"⏳ إعادة المحاولة بعد {retry_delay} ثانية...")
                    time.sleep(retry_delay)
                    continue
                return False

        except Exception as e:
            print(f"❌ خطأ في تحويل Word إلى PDF عبر Gradio Client: {e}")
            if "Connection" in str(e) or "timeout" in str(e).lower():
                print(f"❌ {get_error_message('connection_error')}")

            if attempt < max_retries - 1:
                print(f"⏳ إعادة المحاولة بعد {retry_delay} ثانية...")
                time.sleep(retry_delay)
                continue
            return False

    print(f"❌ فشل في تحويل Word إلى PDF بعد {max_retries} محاولات")
    return False

def process_single_serial_request():
    """
    معالجة طلب رقم تسلسلي واحد مباشرة - بدون طوابير أو حلقات لا نهائية
    """
    try:
        with serial_number_lock:
            # قراءة الرقم التسلسلي الحالي
            serial_number = load_serial_number()

            # زيادة الرقم التسلسلي
            serial_number += 1

            # حفظ الرقم الجديد
            save_serial_number(serial_number)

            print(f"✅ تم إنتاج الرقم التسلسلي: {serial_number}")
            return {'success': True, 'serial_number': str(serial_number)}

    except Exception as e:
        # في حالة الخطأ، إرسال رقم بديل
        fallback_number = int(time.time() * 1000) % 1000000
        print(f"⚠️ خطأ في الرقم التسلسلي، تم استخدام رقم بديل: {fallback_number}")
        return {'success': False, 'serial_number': str(fallback_number), 'error': str(e)}

def serial_number_worker():
    """
    عامل معالجة الرقم التسلسلي - يعمل فقط عند وجود طلبات في الطابور
    لا يعمل في حلقة لا نهائية لتوفير موارد السيرفر
    """
    global serial_number_worker_running
    serial_number_worker_running = True

    print("🟢 تم بدء عامل الرقم التسلسلي")

    # معالجة الطلبات الموجودة في الطابور فقط
    while not serial_number_queue.empty():
        try:
            # محاولة الحصول على طلب من الطابور بدون انتظار
            request_data = serial_number_queue.get_nowait()

            if request_data is None:  # إشارة الإيقاف
                break

            result_queue = request_data['result_queue']

            # معالجة الطلب مباشرة
            result = process_single_serial_request()
            result_queue.put(result)

            # تأكيد انتهاء المعالجة
            serial_number_queue.task_done()

        except queue.Empty:
            # لا توجد طلبات - الخروج من الحلقة
            break
        except Exception as e:
            print(f"❌ خطأ في عامل الرقم التسلسلي: {e}")
            # في حالة الخطأ، نكمل للطلب التالي
            continue

    serial_number_worker_running = False
    print("🔴 تم إيقاف عامل الرقم التسلسلي")

def process_single_cleanup_task(task_type, task_data=None):
    """
    معالجة مهمة تنظيف واحدة مباشرة - بدون طوابير أو حلقات لا نهائية
    """
    try:
        if task_type == 'user_files':
            # تنظيف ملفات مستخدم محدد
            computer_id = task_data.get('computer_id') if task_data else None
            if computer_id:
                _cleanup_user_files_internal(computer_id)

        elif task_type == 'uploads_folder':
            # تنظيف مجلد uploads
            _cleanup_uploads_folder_internal()

        elif task_type == 'hanging_processes':
            # تنظيف العمليات المعلقة
            cleanup_hanging_processes()

        elif task_type == 'memory_cleanup':
            # تنظيف الذاكرة
            import gc
            gc.collect()
            print("🧹 تم تنظيف الذاكرة")

        print(f"✅ تم إنجاز مهمة التنظيف: {task_type}")
        return True

    except Exception as e:
        print(f"❌ خطأ في مهمة التنظيف {task_type}: {e}")
        return False

def cleanup_worker():
    """
    عامل التنظيف المركزي - يعمل فقط عند وجود مهام في الطابور
    لا يعمل في حلقة لا نهائية لتوفير موارد السيرفر
    """
    global cleanup_worker_running
    cleanup_worker_running = True

    print("🧹 تم بدء عامل التنظيف المركزي")

    # معالجة المهام الموجودة في الطابور فقط
    while not cleanup_queue.empty():
        try:
            # محاولة الحصول على مهمة من الطابور بدون انتظار
            cleanup_task = cleanup_queue.get_nowait()

            if cleanup_task is None:  # إشارة الإيقاف
                break

            task_type = cleanup_task.get('type')
            task_data = cleanup_task.get('data', {})

            # معالجة المهمة مباشرة
            process_single_cleanup_task(task_type, task_data)

            # تأكيد انتهاء المعالجة
            cleanup_queue.task_done()

        except queue.Empty:
            # لا توجد مهام - الخروج من الحلقة
            break
        except Exception as e:
            print(f"❌ خطأ في عامل التنظيف: {e}")
            # في حالة الخطأ، نكمل للمهمة التالية
            continue

    cleanup_worker_running = False
    print("🔴 تم إيقاف عامل التنظيف المركزي")

def start_cleanup_worker():
    """بدء عامل التنظيف المركزي إذا لم يكن يعمل"""
    global cleanup_worker_running
    if not cleanup_worker_running:
        worker_thread = threading.Thread(target=cleanup_worker, daemon=True)
        worker_thread.start()
        print("🟢 تم بدء عامل التنظيف المركزي")

def schedule_cleanup_task(task_type, task_data=None):
    """جدولة مهمة تنظيف - محسنة لتوفير موارد السيرفر"""

    # إذا لم يكن هناك مهام في الطابور، معالجة مباشرة لتوفير الموارد
    if cleanup_queue.empty() and not cleanup_worker_running:
        success = process_single_cleanup_task(task_type, task_data)
        if success:
            print(f"✅ تم تنفيذ مهمة التنظيف مباشرة: {task_type}")
        return success

    # إذا كان هناك ضغط على النظام، استخدم الطابور
    try:
        cleanup_task = {
            'type': task_type,
            'data': task_data or {},
            'timestamp': time.time()
        }
        cleanup_queue.put(cleanup_task, timeout=5)
        print(f"📋 تم جدولة مهمة تنظيف: {task_type}")

        # بدء عامل التنظيف إذا لم يكن يعمل
        start_cleanup_worker()
        return True

    except queue.Full:
        # الطابور ممتلئ، معالجة مباشرة كبديل
        print(f"⚠️ طابور التنظيف ممتلئ - معالجة مباشرة: {task_type}")
        return process_single_cleanup_task(task_type, task_data)

    except Exception as e:
        print(f"❌ خطأ في جدولة مهمة التنظيف {task_type}: {e}")
        # محاولة معالجة مباشرة كبديل
        return process_single_cleanup_task(task_type, task_data)

# دالة للتحقق من حالة Gradio Space (محسنة للموارد)
def check_gradio_api_status():
    """التحقق من أن Gradio Space يعمل بشكل صحيح باستخدام المدير المحسن"""
    try:
        print(f"🔍 فحص حالة Gradio Space باستخدام المدير المحسن: {GRADIO_SPACE_ID}")

        # استخدام المدير المحسن للفحص
        client = gradio_manager.get_client()

        if client is None:
            print("❌ فشل في الحصول على Gradio Client")
            return False

        # التحقق من معلومات Space
        print("✅ Gradio Space متاح ويعمل بشكل صحيح")
        return True

    except Exception as e:
        print(f"❌ Gradio Space غير متاح: {e}")
        print(f"   تأكد من أن Space '{GRADIO_SPACE_ID}' يعمل على Hugging Face")
        return False

# بدء عامل الرقم التسلسلي
def start_serial_number_worker():
    """بدء عامل معالجة الرقم التسلسلي إذا لم يكن يعمل"""
    global serial_number_worker_running
    if not serial_number_worker_running:
        worker_thread = threading.Thread(target=serial_number_worker, daemon=True)
        worker_thread.start()
        print("🟢 تم بدء عامل الرقم التسلسلي")

# تم نقل جميع دوال إدارة البيانات إلى firebase_manager.py

# دالة لحذف الصور من مجلد uploads للمستخدم
def cleanup_user_uploads(computer_id):
    """حذف جميع الصور الخاصة بالمستخدم من مجلد uploads"""
    try:
        for filename in os.listdir(UPLOAD_FOLDER):
            if filename.startswith(f'computer_{computer_id}_') or filename.startswith(f'buyer_{computer_id}') or filename.startswith(f'seller_{computer_id}'):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                try:
                    os.remove(file_path)
                    print(f"تم حذف الصورة: {filename}")
                except Exception as e:
                    print(f"خطأ في حذف الصورة {filename}: {e}")
    except Exception as e:
        print(f"خطأ في تنظيف صور المستخدم: {e}")

# تم نقل دوال إدارة العقود المحفوظة إلى firebase_manager.py

# تم نقل دالة update_user_permissions إلى firebase_manager.py

# دالة للتحقق من صلاحيات الإدارة
def is_admin(session):
    return session.get('is_admin', False)

# دالة لتنظيف الصور القديمة (اختيارية)
def cleanup_old_photos():
    try:
        for filename in os.listdir(UPLOAD_FOLDER):
            if filename.endswith(('.png', '.jpg', '.jpeg')):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                # حذف الصور الأقدم من 5 دقائق (300 ثانية) للحذف السريع
                if os.path.getmtime(file_path) < time.time() - 300:
                    os.remove(file_path)
                    print(f"تم حذف الصورة القديمة: {filename}")
    except Exception as e:
        print(f"خطأ في تنظيف الصور القديمة: {e}")

# دالة لحفظ الصور مع معرف الحاسوب
def save_photo(photo_data, photo_type):
    computer_id = session.get('computer_id', 'unknown')
    timestamp = int(time.time())
    filename = f"computer_{computer_id}_{photo_type}_{timestamp}.png"

    photo_data = photo_data.replace('data:image/png;base64,', '')  # إزالة البيانات المبدئية من Base64
    photo_bytes = base64.b64decode(photo_data)  # فك ترميز Base64
    photo_path = os.path.join(UPLOAD_FOLDER, filename)  # تحديد المسار النهائي
    with open(photo_path, 'wb') as f:
        f.write(photo_bytes)  # حفظ الصورة
    return photo_path

# دالة لتوليد QR code صغير جداً
def generate_qr_code(contract_uuid, serial_number):
    """
    توليد QR code صغير جداً للعقد
    يحتوي على رابط لعرض العقد في نظام QR المنفصل
    """
    try:
        # التأكد من وجود مجلد uploads
        if not os.path.exists(UPLOAD_FOLDER):
            os.makedirs(UPLOAD_FOLDER)

        # إنشاء رابط QR code باستخدام ملف الإعدادات
        qr_url = get_contract_qr_url(contract_uuid)
        print_qr_debug('qr_url', url=qr_url)

        # إنشاء QR code باستخدام الإعدادات المحددة
        error_correction_map = {
            'L': qrcode.constants.ERROR_CORRECT_L,
            'M': qrcode.constants.ERROR_CORRECT_M,
            'Q': qrcode.constants.ERROR_CORRECT_Q,
            'H': qrcode.constants.ERROR_CORRECT_H
        }

        qr = qrcode.QRCode(
            version=QR_SETTINGS['version'],
            error_correction=error_correction_map.get(QR_SETTINGS['error_correction'], qrcode.constants.ERROR_CORRECT_L),
            box_size=QR_SETTINGS['box_size'],
            border=QR_SETTINGS['border'],
        )
        qr.add_data(qr_url)
        qr.make(fit=True)

        # إنشاء صورة QR code
        qr_image = qr.make_image(
            fill_color=QR_SETTINGS['fill_color'],
            back_color=QR_SETTINGS['back_color']
        )

        # حفظ QR code كصورة مؤقتة
        timestamp = int(time.time())
        qr_filename = f"qr_{serial_number}_{timestamp}.png"
        qr_path = os.path.join(UPLOAD_FOLDER, qr_filename)
        qr_image.save(qr_path)

        print_qr_debug('qr_generated', path=qr_path)
        return qr_path

    except Exception as e:
        print_qr_debug('qr_error', error=str(e))
        return None

def save_qr_code_to_firebase(contract_id, qr_code_path):
    """
    حفظ QR code جديد في Firebase
    """
    try:
        if qr_code_path and os.path.exists(qr_code_path):
            # تحويل QR code إلى base64
            with open(qr_code_path, 'rb') as qr_file:
                qr_code_base64 = base64.b64encode(qr_file.read()).decode('utf-8')

            # حفظ في Firebase
            from firebase_manager import firebase_manager
            firebase_path = f"finalized_contracts/{contract_id}/images/qrCode"
            firebase_manager.set_data(firebase_path, qr_code_base64)
            print(f"✅ تم حفظ QR Code جديد في Firebase للعقد {contract_id}")
            return True
    except Exception as e:
        print(f"❌ خطأ في حفظ QR Code في Firebase للعقد {contract_id}: {e}")
    return False

# تم نقل دالة check_if_serial_exists إلى firebase_manager.py

def process_pdf_with_gradio(word_path, pdf_path, contract_id):
    """
    دالة لمعالجة PDF باستخدام Gradio API حصرياً
    """
    try:
        print(f"🔄 بدء معالجة PDF للعقد عبر Gradio: {contract_id}")

        # تحويل Word إلى PDF عبر Gradio API
        pdf_converted = convert_word_to_pdf_via_gradio(word_path, pdf_path)

        if pdf_converted and os.path.exists(pdf_path):
            return {
                'success': True,
                'pdf_path': pdf_path,
                'contract_id': contract_id,
                'message': 'تم تحويل العقد إلى PDF بنجاح عبر Gradio API'
            }
        else:
            return {
                'success': False,
                'contract_id': contract_id,
                'error': 'فشل في تحويل العقد إلى PDF عبر Gradio API'
            }

    except Exception as e:
        return {
            'success': False,
            'contract_id': contract_id,
            'error': f'خطأ في معالجة PDF عبر Gradio: {str(e)}'
        }

def get_and_increment_serial_number(use_existing_serial=None):
    """
    دالة آمنة للحصول على الرقم التسلسلي - محسنة لتوفير موارد السيرفر
    تعمل مباشرة عندما لا توجد ضغوط، وتستخدم الطابور فقط عند الحاجة

    Args:
        use_existing_serial: إذا تم تمرير رقم موجود، سيتم استخدامه بدلاً من إنشاء رقم جديد
    """
    # إذا تم تمرير رقم موجود وهو صالح، استخدمه
    if use_existing_serial is not None:
        try:
            existing_serial = int(use_existing_serial)
            if existing_serial > 0:
                print(f"🔄 استخدام الرقم التسلسلي الموجود: {existing_serial}")
                return existing_serial
        except (ValueError, TypeError):
            print(f"⚠️ الرقم التسلسلي الموجود غير صالح: {use_existing_serial}")

    # إذا لم يكن هناك طلبات في الطابور، معالجة مباشرة لتوفير الموارد
    if serial_number_queue.empty() and not serial_number_worker_running:
        result = process_single_serial_request()
        if result['success']:
            return result['serial_number']
        else:
            print(f"⚠️ تم استخدام رقم بديل بسبب خطأ: {result.get('error', 'غير معروف')}")
            return result['serial_number']

    # إذا كان هناك ضغط على النظام، استخدم الطابور
    # التأكد من تشغيل عامل الرقم التسلسلي
    start_serial_number_worker()

    # إنشاء طابور للنتيجة
    result_queue = queue.Queue()

    # إضافة الطلب إلى الطابور
    request_data = {
        'result_queue': result_queue,
        'timestamp': time.time()
    }

    try:
        # إضافة الطلب إلى طابور المعالجة
        serial_number_queue.put(request_data, timeout=5)

        # انتظار النتيجة (حد أقصى 10 ثوانٍ)
        result = result_queue.get(timeout=10)

        if result['success']:
            return result['serial_number']
        else:
            print(f"⚠️ تم استخدام رقم بديل بسبب خطأ: {result.get('error', 'غير معروف')}")
            return result['serial_number']

    except queue.Full:
        # الطابور ممتلئ، معالجة مباشرة كبديل
        print("⚠️ الطابور ممتلئ، معالجة مباشرة...")
        result = process_single_serial_request()
        return result['serial_number']

    except queue.Empty:
        # انتهت مهلة الانتظار، معالجة مباشرة كبديل
        print("⚠️ انتهت مهلة الانتظار، معالجة مباشرة...")
        result = process_single_serial_request()
        return result['serial_number']

    except Exception as e:
        # خطأ غير متوقع، معالجة مباشرة كبديل
        print(f"❌ خطأ غير متوقع: {e}، معالجة مباشرة...")
        result = process_single_serial_request()
        return result['serial_number']

# تم استبدال دالة convert_word_to_pdf بـ convert_word_to_pdf_via_gradio
# جميع عمليات التحويل تتم الآن عبر Gradio API حصرياً


# تم حذف دوال LibreOffice - يتم استخدام Gradio API حصرياً


# تم حذف دوال docx2pdf - يتم استخدام Gradio API حصرياً

def fill_word_template(template_path, output_path, data, seller_photo_path=None, buyer_photo_path=None, qr_code_path=None):
    doc = Document(template_path)

    # استبدال النصوص في الفقرات مع الحفاظ على التنسيق
    for para in doc.paragraphs:
        full_text = "".join(run.text for run in para.runs)  # دمج النصوص في الفقرة

        # التحقق من وجود QR code في الفقرة
        if "{{qr}}" in full_text and qr_code_path and os.path.exists(qr_code_path):
            # إزالة النص المتعلق بـ QR code
            full_text = full_text.replace("{{qr}}", "")
            # تنظيف النص في جميع runs
            for run in para.runs:
                run.text = ""
            para.runs[0].text = full_text
            # إضافة QR code صغير جداً في الفقرة
            run = para.add_run()
            run.add_picture(
                qr_code_path,
                width=Inches(WORD_QR_SIZE['width_inches']),
                height=Inches(WORD_QR_SIZE['height_inches'])
            )
            para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            print_qr_debug('qr_inserted_paragraph', path=qr_code_path)

        # استبدال باقي النصوص
        for key, value in data.items():
            placeholder = f"{{{{{key}}}}}"
            if placeholder in full_text:
                # استبدال النصوص دفعة واحدة
                full_text = full_text.replace(placeholder, value)
                # تقسيم النص المحدث على نفس كائنات run للحفاظ على التنسيق
                for run in para.runs:
                    run.text = ""
                para.runs[0].text = full_text

    # استبدال النصوص في الجداول مع نفس الأسلوب
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for para in cell.paragraphs:
                    full_text = "".join(run.text for run in para.runs)
                    for key, value in data.items():
                        placeholder = f"{{{{{key}}}}}"
                        if placeholder in full_text:
                            full_text = full_text.replace(placeholder, value)
                            for run in para.runs:
                                run.text = ""
                            para.runs[0].text = full_text

                # إدراج صورة البائع
                if "{{seller_photo}}" in cell.text and seller_photo_path:
                    cell.text = cell.text.replace("{{seller_photo}}", "")  # إزالة النص المتعلق بالصورة
                    paragraph = cell.paragraphs[0]
                    # إضافة الصورة في الفقرة في الخلية
                    run = paragraph.add_run()
                    run.add_picture(seller_photo_path, width=Inches(1), height=Inches(1.3))
                    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT

                # إدراج صورة المشتري
                if "{{buyer_photo}}" in cell.text and buyer_photo_path:
                    cell.text = cell.text.replace("{{buyer_photo}}", "")  # إزالة النص المتعلق بالصورة
                    paragraph = cell.paragraphs[0]
                    # إضافة الصورة في الفقرة في الخلية
                    run = paragraph.add_run()
                    run.add_picture(buyer_photo_path, width=Inches(1), height=Inches(1.3))
                    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT

                # إدراج QR code صغير جداً
                if "{{qr}}" in cell.text and qr_code_path and os.path.exists(qr_code_path):
                    cell.text = cell.text.replace("{{qr}}", "")  # إزالة النص المتعلق بـ QR code
                    paragraph = cell.paragraphs[0]
                    # إضافة QR code صغير جداً في الفقرة باستخدام الإعدادات
                    run = paragraph.add_run()
                    run.add_picture(
                        qr_code_path,
                        width=Inches(WORD_QR_SIZE['width_inches']),
                        height=Inches(WORD_QR_SIZE['height_inches'])
                    )
                    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                    print_qr_debug('qr_inserted', path=qr_code_path)

    # حفظ الملف بعد التعديل
    doc.save(output_path)

# الصفحة الرئيسية - توجيه إلى تسجيل الدخول
@app.route('/start')
def start():
    return redirect(url_for('login'))

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        computer_name = request.form.get('computer_name', '').strip()
        password = request.form.get('password', '').strip()

        if not computer_name or not password:
            return render_template('login.html', error='يرجى إدخال اسم الحاسوب وكلمة المرور')

        computers = load_computers()

        # إذا كان الحاسوب موجود، تحقق من كلمة المرور
        if computer_name in computers:
            if computers[computer_name]['password'] == password:
                session['computer_id'] = computers[computer_name]['id']
                session['computer_name'] = computer_name
                session['is_admin'] = False  # المستخدمون العاديون ليسوا إداريين
                return redirect(url_for('form_page'))
            else:
                return render_template('login.html', error='كلمة المرور غير صحيحة')
        else:
            return render_template('login.html', error='الحاسوب غير مسجل. يرجى التواصل مع الإدارة لإنشاء حساب.')

    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

# صفحة الإدارة
@app.route('/admin', methods=['GET', 'POST'])
def admin():
    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'login':
            password = request.form.get('admin_password', '').strip()
            admin_config = load_admin_config()

            if password == admin_config['password']:
                session['is_admin'] = True
                session['computer_name'] = 'الإدارة'
                session['computer_id'] = 'admin'
                return redirect(url_for('admin_dashboard'))
            else:
                return render_template('admin_login.html', error='كلمة مرور الإدارة غير صحيحة')

        elif action == 'create_user' and is_admin(session):
            computer_name = request.form.get('computer_name', '').strip()
            password = request.form.get('password', '').strip()

            if not computer_name or not password:
                computers = load_computers()
                current_serial = load_serial_number()
                return render_template('admin_dashboard.html',
                                     computers=computers,
                                     current_serial=current_serial,
                                     error='يرجى إدخال اسم الحاسوب وكلمة المرور')

            computers = load_computers()

            if computer_name in computers:
                current_serial = load_serial_number()
                return render_template('admin_dashboard.html',
                                     computers=computers,
                                     current_serial=current_serial,
                                     error='اسم الحاسوب موجود مسبقاً')

            # إنشاء حاسوب جديد
            computer_id = len(computers) + 1
            computers[computer_name] = {
                'id': computer_id,
                'password': password,
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'contracts_count': 0,
                'permissions': {
                    'edit_finalized_contracts': False,
                    'download_finalized_contracts': False
                },
                'has_saved_contract': False
            }
            save_computers(computers)
            current_serial = load_serial_number()
            return render_template('admin_dashboard.html',
                                 computers=computers,
                                 current_serial=current_serial,
                                 success=f'تم إنشاء حساب {computer_name} بنجاح')

        elif action == 'change_password' and is_admin(session):
            new_password = request.form.get('new_admin_password', '').strip()
            computers = load_computers()
            current_serial = load_serial_number()
            if new_password:
                admin_config = load_admin_config()
                admin_config['password'] = new_password
                save_admin_config(admin_config)
                return render_template('admin_dashboard.html',
                                     computers=computers,
                                     current_serial=current_serial,
                                     success='تم تغيير كلمة مرور الإدارة بنجاح')
            else:
                return render_template('admin_dashboard.html',
                                     computers=computers,
                                     current_serial=current_serial,
                                     error='يرجى إدخال كلمة مرور جديدة')

    # إذا كان الإداري مسجل دخول، توجيه إلى لوحة التحكم
    if is_admin(session):
        return redirect(url_for('admin_dashboard'))

    return render_template('admin_login.html')

# لوحة تحكم الإدارة
@app.route('/admin/dashboard')
def admin_dashboard():
    if not is_admin(session):
        return redirect(url_for('admin'))

    computers = load_computers()
    current_serial = load_serial_number()

    return render_template('admin_dashboard.html',
                         computers=computers,
                         current_serial=current_serial)

# صفحة تحميل عقود السنة
@app.route('/admin/annual_export')
def annual_export():
    if not is_admin(session):
        return redirect(url_for('admin'))

    return render_template('annual_export.html')

# عرض قائمة الحاسبات (للإداريين فقط)
@app.route('/computers')
def computers_list():
    if not is_admin(session):
        return redirect(url_for('login'))

    return render_template('computers_list.html')

# API لجلب بيانات الحاسبات (للإداريين فقط)
@app.route('/admin/api/computers')
def get_computers_api():
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    try:
        computers = load_computers()
        return {'success': True, 'computers': computers}
    except Exception as e:
        return {'success': False, 'error': str(e)}, 500

@app.route('/', methods=['GET'])
def form_page():
    # التحقق من تسجيل الدخول
    if 'computer_id' not in session:
        return redirect(url_for('login'))
    return render_template('index.html',
                         computer_id=session.get('computer_id'),
                         computer_name=session.get('computer_name'))

@app.route('/', methods=['POST'])
def index():
    # تحديث نشاط الطلبات
    update_request_activity()

    # التحقق من تسجيل الدخول
    if 'computer_id' not in session:
        return redirect(url_for('login'))

    # تنظيف الصور القديمة قبل البدء
    cleanup_old_photos()

    computer_id = session.get('computer_id')
    computer_name = session.get('computer_name')

    # التحقق من وجود عقد محفوظ وما إذا كان المستخدم يعدل عقد موجود
    is_editing_saved = request.form.get('is_editing_saved', 'false') == 'true'

    # التحقق من وجود رقم تسلسلي من ملف محلي
    local_serial_number = request.form.get('local_serial_number', '').strip()

    # التحقق من وجود رقم تسلسلي يدوي
    manual_serial_number = request.form.get('manual_serial_number', '').strip()

    if is_editing_saved:
        # استخدام الرقم التسلسلي من العقد المحفوظ
        saved_contract = load_saved_contract(computer_name)
        if saved_contract:
            serial_number = saved_contract['contract_info']['serial_number']
        else:
            # إذا لم يوجد عقد محفوظ، احصل على رقم جديد
            serial_number = get_and_increment_serial_number()
            if computer_name:
                increment_user_contracts(computer_name)
    elif manual_serial_number:
        # استخدام الرقم التسلسلي اليدوي (لا نزيد العداد)
        try:
            manual_serial = int(manual_serial_number)
            if manual_serial > 0:
                serial_number = manual_serial
                print(f"🔢 استخدام الرقم التسلسلي اليدوي: {serial_number}")
            else:
                # رقم غير صالح، احصل على رقم جديد
                serial_number = get_and_increment_serial_number()
                if computer_name:
                    increment_user_contracts(computer_name)
        except (ValueError, TypeError):
            # رقم غير صالح، احصل على رقم جديد
            serial_number = get_and_increment_serial_number()
            if computer_name:
                increment_user_contracts(computer_name)
    elif local_serial_number:
        # استخدام الرقم التسلسلي من الملف المحلي (لا نزيد العداد)
        try:
            local_serial = int(local_serial_number)
            if local_serial > 0:
                serial_number = local_serial
                print(f"🔄 استخدام الرقم التسلسلي من الملف المحلي: {serial_number}")
            else:
                # رقم غير صالح، احصل على رقم جديد
                serial_number = get_and_increment_serial_number()
                if computer_name:
                    increment_user_contracts(computer_name)
        except (ValueError, TypeError):
            # رقم غير صالح، احصل على رقم جديد
            serial_number = get_and_increment_serial_number()
            if computer_name:
                increment_user_contracts(computer_name)
    else:
        # عقد جديد - احصل على رقم تسلسلي جديد
        serial_number = get_and_increment_serial_number()
        if computer_name:
            increment_user_contracts(computer_name)

    # جمع البيانات من النموذج
    currency_type = request.form.get('currency_type', 'IQD')
    badal_num_raw = request.form.get('badal_num', '').replace(',', '')
    mony_num_raw = request.form.get('mony_num', '').replace(',', '')

    # حساب المبلغ المتبقي داخلياً
    try:
        badal_amount = int(badal_num_raw) if badal_num_raw else 0
        mony_amount = int(mony_num_raw) if mony_num_raw else 0
        remaining_amount = badal_amount - mony_amount
    except ValueError:
        badal_amount = 0
        mony_amount = 0
        remaining_amount = 0

    # تنسيق المبالغ مع النص العربي
    badal_formatted = format_number_with_currency(badal_amount, currency_type)
    badal_text = number_to_arabic_text(badal_amount, currency_type)
    badal_display = badal_formatted
    badal_writing_display = badal_text

    mony_formatted = format_number_with_currency(mony_amount, currency_type)
    mony_text = number_to_arabic_text(mony_amount, currency_type)
    mony_display = mony_formatted
    mony_writing_display = mony_text

    remaining_formatted = format_number_with_currency(remaining_amount, currency_type)
    remaining_text = number_to_arabic_text(remaining_amount, currency_type)
    remaining_display = remaining_formatted
    remaining_arabic_display = remaining_text

    # إنشاء UUID للعقد لاستخدامه في QR code
    contract_uuid = str(uuid.uuid4())

    # توليد QR code للعقد
    qr_code_path = generate_qr_code(contract_uuid, serial_number)

    data = {
        'date': now.strftime('%Y-%m-%d'),
        'date_1': now.strftime('%Y-%m-%d'),
        #'date1': now.strftime('%Y-%m-%d'),

        't': request.form.get('t', ''),
        't_1': request.form.get('t_1', ''),
        't_11': request.form.get('t_1', ''),
        't_': request.form.get('t', ''),
        'day': request.form.get('day', ''),
        'serial_number': serial_number,
        'name_1': request.form.get('name_1', ''),
        'location_1': request.form.get('location_1', ''),
        'name_2': request.form.get('name_2', ''),
        'name_22': request.form.get('name_2', ''),
        'location_2': request.form.get('location_2', ''),
        'id_1': request.form.get('id_1', ''),
        'phone_1': request.form.get('phone_1', ''),
        'name_3': request.form.get('name_3', ''),
        'name_33': request.form.get('name_3', ''),
        'location_3': request.form.get('location_3', ''),
        'id_2': request.form.get('id_2', ''),
        'phone_2': request.form.get('phone_2', ''),
        'sin_num': request.form.get('sin_num', ''),
        'car_num': request.form.get('car_num', ''),
        'car_prop': request.form.get('car_prop', ''),
        'car_type': request.form.get('car_type', ''),
        'car_model': request.form.get('car_model', ''),
        'car_colar': request.form.get('car_colar', ''),
        'sasi_num': request.form.get('sasi_num', ''),
        'badal_num': badal_display,  # المبلغ
        'badal_writing': badal_writing_display, # النص العربي
        'mony_num': mony_display,    # المبلغ
        'mony_writing': mony_writing_display, # النص العربي
        'mony_not_delevired': remaining_display,  # المبلغ المتبقي
        'mony_not_delevired_writing': remaining_arabic_display, # النص العربي للمبلغ المتبقي
        'note_a': request.form.get('note_a', ''),
        'note_b': request.form.get('note_b', ''),
        'm1': request.form.get('m1', ''),
        'car_city': request.form.get('car_city', ''),
        'currency_type': currency_type,
        'uuid': contract_uuid
    }

    # استخراج الصور Base64 من الحقول
    seller_photo_base64 = request.form.get('sellerPhoto')
    buyer_photo_base64 = request.form.get('buyerPhoto')

    # حفظ الصور إذا كانت موجودة
    seller_photo_path = None
    buyer_photo_path = None
    if seller_photo_base64:
        seller_photo_path = save_photo(seller_photo_base64, 'seller')

    if buyer_photo_base64:
        buyer_photo_path = save_photo(buyer_photo_base64, 'buyer')

    # تعبئة الملف بالبيانات مع QR code
    fill_word_template('template.docx', 'filled_template.docx', data, seller_photo_path=seller_photo_path, buyer_photo_path=buyer_photo_path, qr_code_path=qr_code_path)

    # حفظ بيانات العقد كمسودة تلقائياً (مع الصور)
    contract_data = {k: v for k, v in data.items() if k not in ['seller_photo', 'buyer_photo']}
    contract_data['serial_number'] = serial_number

    if not is_editing_saved:
        contract_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # تحضير الصور للحفظ
    images = {}
    if seller_photo_base64:
        images['sellerPhoto'] = seller_photo_base64
    if buyer_photo_base64:
        images['buyerPhoto'] = buyer_photo_base64

    # حفظ العقد كمسودة تلقائياً بعد إنشاء مستند Word
    save_contract_data(computer_name, contract_data, is_editing_saved, images)

    print(f"✅ تم حفظ العقد رقم {serial_number} كمسودة للمستخدم {computer_name}")

    # حذف الصور من مجلد uploads بعد حفظها في ملف الوورد
    if seller_photo_path and os.path.exists(seller_photo_path):
        try:
            os.remove(seller_photo_path)
            print(f"تم حذف صورة البائع: {seller_photo_path}")
        except Exception as e:
            print(f"خطأ في حذف صورة البائع: {e}")

    if buyer_photo_path and os.path.exists(buyer_photo_path):
        try:
            os.remove(buyer_photo_path)
            print(f"تم حذف صورة المشتري: {buyer_photo_path}")
        except Exception as e:
            print(f"خطأ في حذف صورة المشتري: {e}")

    # حذف QR code المؤقت
    if qr_code_path and os.path.exists(qr_code_path):
        try:
            os.remove(qr_code_path)
            print(f"تم حذف QR code: {qr_code_path}")
        except Exception as e:
            print(f"خطأ في حذف QR code: {e}")

    # تحميل الملف بعد تعديله مع الحذف التلقائي
    filled_template_path = os.path.join(os.getcwd(), 'filled_template.docx')
    return send_file_with_auto_cleanup(filled_template_path, 'filled_template.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')

@app.route('/new_page')
def new_page():
    # التحقق من تسجيل الدخول
    if 'computer_id' not in session:
        return redirect(url_for('login'))

    # التحقق من صلاحيات الإدارة
    if not is_admin(session):
        return redirect(url_for('form_page'))

    current_serial = load_serial_number()
    return render_template('new.html', current_serial=current_serial)

@app.route('/new_page', methods=['GET', 'POST'])
def edit_txt():
    # التحقق من تسجيل الدخول
    if 'computer_id' not in session:
        return redirect(url_for('login'))

    # التحقق من صلاحيات الإدارة
    if not is_admin(session):
        return redirect(url_for('form_page'))

    if request.method == 'POST':
        new_content = request.form.get('file_content')
        if new_content:
            try:
                # التحقق من أن المحتوى رقم صحيح
                new_number = int(new_content.strip())

                # حفظ الرقم الجديد بطريقة آمنة
                with serial_number_lock:
                    save_serial_number(new_number)

                # رسالة تأكيدية بعد التعديل
                return render_template('new.html', message="تم تحديث الرقم التسلسلي بنجاح!",
                                     file_content=new_content, current_serial=new_number)
            except ValueError:
                current_serial = load_serial_number()
                return render_template('new.html', message="خطأ: يجب أن يكون المحتوى رقماً صحيحاً!",
                                     file_content=new_content, current_serial=current_serial)
            except Exception as e:
                current_serial = load_serial_number()
                return render_template('new.html', message=f"خطأ في التحديث: {str(e)}",
                                     file_content=new_content, current_serial=current_serial)

    # قراءة الرقم التسلسلي الحالي
    current_serial = load_serial_number()
    return render_template('new.html', file_content=str(current_serial), current_serial=current_serial, message=None)

# API لجلب الرقم التسلسلي الحالي
@app.route('/api/current_serial')
def get_current_serial():
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    current_serial = load_serial_number()
    return {'current_serial': current_serial}

# دالة مساعدة للحصول على الرقم التسلسلي الحالي
def get_current_serial_number():
    return load_serial_number()

# دالة مساعدة للحصول على إحصائيات المستخدمين
def get_all_user_stats():
    computers = load_computers()
    return computers

# API لإدارة العقد المحفوظ
@app.route('/api/saved_contract', methods=['GET', 'POST', 'DELETE'])
def manage_saved_contract():
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    computer_id = session.get('computer_id')
    computer_name = session.get('computer_name')

    if request.method == 'GET':
        # جلب العقد المحفوظ
        contract = load_saved_contract(computer_name)
        if contract:
            return {'success': True, 'contract': contract}
        else:
            return {'success': False, 'message': 'لا يوجد عقد محفوظ'}

    elif request.method == 'POST':
        # حفظ العقد
        if not check_permission(computer_name, 'edit_saved_contracts'):
            return {'error': 'ليس لديك صلاحية لحفظ العقود'}, 403

        contract_data = request.json
        is_editing = contract_data.get('is_editing', False)

        # إذا لم يكن في وضع التعديل، احصل على رقم تسلسلي جديد
        if not is_editing:
            serial_number = get_and_increment_serial_number()
            contract_data['serial_number'] = serial_number
            contract_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # هذا API يستخدم JSON فقط، لا يدعم الصور
        save_contract_data(computer_name, contract_data, is_editing)
        return {'success': True, 'message': 'تم حفظ العقد بنجاح'}

    elif request.method == 'DELETE':
        # حذف العقد المحفوظ
        if not check_permission(computer_name, 'edit_saved_contracts'):
            return {'error': 'ليس لديك صلاحية لحذف العقود'}, 403

        if delete_saved_contract(computer_name):
            return {'success': True, 'message': 'تم حذف العقد بنجاح'}
        else:
            return {'success': False, 'message': 'لا يوجد عقد محفوظ للحذف'}

# API لحفظ العقد كمسودة (بدون إنتاج مستند Word)
@app.route('/api/save_contract', methods=['POST'])
def save_contract_draft():
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    computer_name = session.get('computer_name')
    if not check_permission(computer_name, 'edit_saved_contracts'):
        return {'error': 'ليس لديك صلاحية لحفظ العقود'}, 403

    computer_id = session.get('computer_id')

    # التحقق من نوع البيانات المرسلة
    if request.content_type and 'application/json' in request.content_type:
        # البيانات القديمة (JSON) - للتوافق مع النسخة السابقة
        contract_data = request.json
        images = {}
    else:
        # البيانات الجديدة (FormData) - تتضمن الصور
        contract_data = {}
        for key, value in request.form.items():
            if key not in ['buyerPhoto', 'sellerPhoto']:
                contract_data[key] = value

        # استخراج الصور
        images = {}
        if 'buyerPhoto' in request.form and request.form['buyerPhoto']:
            images['buyerPhoto'] = request.form['buyerPhoto']
        if 'sellerPhoto' in request.form and request.form['sellerPhoto']:
            images['sellerPhoto'] = request.form['sellerPhoto']

    # حفظ كمسودة (بدون رقم تسلسلي جديد إذا كان موجود)
    existing_contract = load_saved_contract(computer_name)
    if existing_contract:
        contract_data['serial_number'] = existing_contract['contract_info']['serial_number']
        contract_data['created_at'] = existing_contract['contract_info']['created_at']
        is_editing = True
    else:
        # عقد جديد - احصل على رقم تسلسلي
        serial_number = get_and_increment_serial_number()
        contract_data['serial_number'] = serial_number
        contract_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        is_editing = False

    save_contract_data(computer_name, contract_data, is_editing, images)
    return {'success': True, 'message': 'تم حفظ المسودة بنجاح مع الصور'}

# API لعرض العقد في صفحة الطباعة (في حالة فشل PDF)
@app.route('/print_contract/<contract_id>')
def print_contract(contract_id):
    if 'computer_id' not in session:
        return redirect(url_for('login'))

    try:
        # البحث عن العقد في Firebase
        from firebase_manager import firebase_manager
        firebase_path = f"finalized_contracts/{contract_id}"
        contract_data = firebase_manager.get_data(firebase_path)

        if not contract_data:
            return render_template('error.html', error='العقد غير موجود'), 404

        contract_info = contract_data.get('contract_data', {})
        images = contract_data.get('images', {})
        pdf_converted = contract_data.get('pdf_converted', False)

        # التحقق من وجود QR Code وتوليده إذا لم يكن موجوداً
        qr_code_generated_new = False
        if not images.get('qrCode'):
            contract_uuid = contract_data.get('uuid')
            if not contract_uuid:
                # إنشاء UUID جديد إذا لم يكن موجوداً
                contract_uuid = str(uuid.uuid4())
                firebase_manager.set_data(f"finalized_contracts/{contract_id}/uuid", contract_uuid)
                print(f"✅ تم إنشاء UUID جديد للعقد {contract_id}: {contract_uuid}")

            # توليد QR code جديد
            qr_code_path = generate_qr_code(contract_uuid, contract_info.get('serial_number', ''))
            if qr_code_path:
                qr_code_generated_new = True
                save_qr_code_to_firebase(contract_id, qr_code_path)
                print(f"✅ تم توليد وحفظ QR Code جديد للعقد {contract_id}")

                # حذف الملف المؤقت
                try:
                    os.remove(qr_code_path)
                except:
                    pass

        # إعادة تحميل البيانات من Firebase إذا تم توليد باركود جديد
        if qr_code_generated_new:
            contract_data = firebase_manager.get_data(firebase_path)
            images = contract_data.get('images', {})

        return render_template('print_contract.html',
                             contract=contract_info,
                             images=images,
                             contract_id=contract_id,
                             pdf_failed=not pdf_converted)

    except Exception as e:
        return render_template('error.html', error=f'خطأ في عرض العقد: {str(e)}'), 500

# API لعرض العقد كـ PDF في المتصفح مباشرة
@app.route('/view_contract_pdf/<contract_id>')
def view_contract_pdf(contract_id):
    if 'computer_id' not in session:
        return redirect(url_for('login'))

    try:
        from firebase_manager import firebase_manager
        import time
        import threading

        # جلب بيانات العقد من Firebase
        firebase_path = f"finalized_contracts/{contract_id}"
        contract_data = firebase_manager.get_data(firebase_path)

        if not contract_data:
            return "العقد غير موجود", 404

        contract_info = contract_data.get('contract_data', {})
        images = contract_data.get('images', {})

        # إنشاء ملفات مؤقتة للصور
        seller_photo_path = None
        buyer_photo_path = None
        qr_code_path = None

        if images.get('sellerPhoto'):
            seller_photo_data = base64.b64decode(images['sellerPhoto'])
            seller_photo_path = os.path.join(UPLOAD_FOLDER, f'temp_seller_{contract_id}_{int(time.time())}.jpg')
            with open(seller_photo_path, 'wb') as f:
                f.write(seller_photo_data)

        if images.get('buyerPhoto'):
            buyer_photo_data = base64.b64decode(images['buyerPhoto'])
            buyer_photo_path = os.path.join(UPLOAD_FOLDER, f'temp_buyer_{contract_id}_{int(time.time())}.jpg')
            with open(buyer_photo_path, 'wb') as f:
                f.write(buyer_photo_data)

        # استخدام QR Code المحفوظ إذا كان متوفراً
        qr_code_path = None
        qr_code_generated_new = False
        if images.get('qrCode'):
            try:
                qr_code_data = base64.b64decode(images['qrCode'])
                qr_code_path = os.path.join(UPLOAD_FOLDER, f'temp_qr_{contract_id}_{int(time.time())}.png')
                with open(qr_code_path, 'wb') as f:
                    f.write(qr_code_data)
                print(f"✅ تم استخدام QR Code المحفوظ لعرض العقد {contract_id}")
            except Exception as e:
                print(f"❌ خطأ في استخدام QR Code المحفوظ لعرض العقد {contract_id}: {e}")
                # في حالة الفشل، توليد QR code جديد إذا كان UUID متوفراً
                contract_uuid = contract_data.get('uuid')
                if contract_uuid:
                    qr_code_path = generate_qr_code(contract_uuid, contract_info.get('serial_number', ''))
                    if qr_code_path:
                        qr_code_generated_new = True
                        save_qr_code_to_firebase(contract_id, qr_code_path)
                else:
                    # إذا لم يكن هناك UUID، إنشاء UUID جديد
                    contract_uuid = str(uuid.uuid4())
                    qr_code_path = generate_qr_code(contract_uuid, contract_info.get('serial_number', ''))
                    if qr_code_path:
                        qr_code_generated_new = True
                        # حفظ UUID الجديد في Firebase
                        from firebase_manager import firebase_manager
                        firebase_manager.set_data(f"finalized_contracts/{contract_id}/uuid", contract_uuid)
                        save_qr_code_to_firebase(contract_id, qr_code_path)
                        print(f"✅ تم إنشاء UUID جديد وحفظه للعقد {contract_id}: {contract_uuid}")
        else:
            # إذا لم يكن QR Code محفوظاً، توليد جديد
            contract_uuid = contract_data.get('uuid')
            if not contract_uuid:
                # إنشاء UUID جديد إذا لم يكن موجوداً
                contract_uuid = str(uuid.uuid4())
                from firebase_manager import firebase_manager
                firebase_manager.set_data(f"finalized_contracts/{contract_id}/uuid", contract_uuid)
                print(f"✅ تم إنشاء UUID جديد للعقد {contract_id}: {contract_uuid}")

            qr_code_path = generate_qr_code(contract_uuid, contract_info.get('serial_number', ''))
            if qr_code_path:
                qr_code_generated_new = True
                save_qr_code_to_firebase(contract_id, qr_code_path)
                print(f"✅ تم توليد وحفظ QR Code جديد للعقد {contract_id}")

        # إعادة تحميل البيانات من Firebase إذا تم توليد باركود جديد
        if qr_code_generated_new:
            contract_data = firebase_manager.get_data(f"finalized_contracts/{contract_id}")
            images = contract_data.get('images', {})

        # إنشاء ملف Word مؤقت
        timestamp = int(time.time())
        temp_word_filename = f'temp_view_contract_{contract_id}_{timestamp}.docx'
        temp_word_path = os.path.join(UPLOAD_FOLDER, temp_word_filename)

        # التأكد من وجود qr_code_path
        if 'qr_code_path' not in locals() or qr_code_path is None:
            qr_code_path = None

        # تعبئة الملف بالبيانات مع QR Code
        fill_word_template('template.docx', temp_word_path, contract_info, seller_photo_path, buyer_photo_path, qr_code_path)

        # تحويل Word إلى PDF عبر Gradio API
        temp_pdf_filename = f'temp_view_contract_{contract_id}_{timestamp}.pdf'
        temp_pdf_path = os.path.join(UPLOAD_FOLDER, temp_pdf_filename)

        pdf_converted = convert_word_to_pdf_via_gradio(temp_word_path, temp_pdf_path)

        if pdf_converted and os.path.exists(temp_pdf_path):
            # دالة لحذف الملفات المؤقتة بعد 30 ثانية
            def cleanup_temp_files():
                try:
                    if seller_photo_path and os.path.exists(seller_photo_path):
                        os.remove(seller_photo_path)
                    if buyer_photo_path and os.path.exists(buyer_photo_path):
                        os.remove(buyer_photo_path)
                    if qr_code_path and os.path.exists(qr_code_path):
                        os.remove(qr_code_path)
                    if os.path.exists(temp_word_path):
                        os.remove(temp_word_path)
                    if os.path.exists(temp_pdf_path):
                        os.remove(temp_pdf_path)
                    print(f"🗑️ تم حذف الملفات المؤقتة للعقد {contract_id}")
                except Exception as e:
                    print(f"خطأ في حذف الملفات المؤقتة: {e}")

            # جدولة حذف الملفات فوراً بعد العرض مع تأخير بسيط
            threading.Timer(5.0, cleanup_temp_files).start()

            # عرض PDF في المتصفح
            return send_file(temp_pdf_path, mimetype='application/pdf')
        else:
            # فشل تحويل PDF - حذف الملفات المؤقتة فوراً
            try:
                if seller_photo_path and os.path.exists(seller_photo_path):
                    os.remove(seller_photo_path)
                if buyer_photo_path and os.path.exists(buyer_photo_path):
                    os.remove(buyer_photo_path)
                if qr_code_path and os.path.exists(qr_code_path):
                    os.remove(qr_code_path)
                if os.path.exists(temp_word_path):
                    os.remove(temp_word_path)
            except:
                pass

            # إعادة توجيه إلى صفحة الطباعة الويب
            return redirect(url_for('print_contract', contract_id=contract_id))

    except Exception as e:
        return f"خطأ في عرض العقد: {str(e)}", 500

# API لتحميل PDF المُنتج
@app.route('/api/download_contract_pdf/<contract_id>')
def download_contract_pdf(contract_id):
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    try:
        # البحث عن العقد في Firebase
        from firebase_manager import firebase_manager
        firebase_path = f"finalized_contracts/{contract_id}"
        contract_data = firebase_manager.get_data(firebase_path)

        if not contract_data:
            return {'error': 'العقد غير موجود'}, 404

        pdf_filename = contract_data.get('pdf_filename')
        if not pdf_filename:
            return {'error': 'ملف PDF غير متوفر لهذا العقد'}, 404

        pdf_path = os.path.join(UPLOAD_FOLDER, pdf_filename)
        if not os.path.exists(pdf_path):
            return {'error': 'ملف PDF غير موجود على الخادم'}, 404

        # استخدام الدالة المخصصة للإرسال مع الحذف التلقائي
        return send_file_with_auto_cleanup(pdf_path, pdf_filename, 'application/pdf')

    except Exception as e:
        return {'error': f'خطأ في تحميل PDF: {str(e)}'}, 500

# API لإبرام العقد وحفظه في Firebase
@app.route('/api/finalize_contract', methods=['POST'])
def finalize_contract():
    # تحديث نشاط الطلبات
    update_request_activity()

    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    try:
        computer_id = session.get('computer_id')
        computer_name = session.get('computer_name')

        print(f"🚀 بدء عملية إبرام العقد للمستخدم: {computer_name}")

        # الحصول على رقم تسلسلي جديد
        serial_number = get_and_increment_serial_number()
        print(f"🔢 تم الحصول على الرقم التسلسلي: {serial_number}")
        if computer_name:
            increment_user_contracts(computer_name)

        # جمع البيانات من النموذج
        currency_type = request.form.get('currency_type', 'IQD')
        badal_num_raw = request.form.get('badal_num', '').replace(',', '')
        mony_num_raw = request.form.get('mony_num', '').replace(',', '')

        # حساب المبلغ المتبقي
        try:
            badal_amount = int(badal_num_raw) if badal_num_raw else 0
            mony_amount = int(mony_num_raw) if mony_num_raw else 0
            remaining_amount = badal_amount - mony_amount
        except ValueError:
            badal_amount = 0
            mony_amount = 0
            remaining_amount = 0

        # تنسيق المبالغ مع النص العربي
        badal_formatted = format_number_with_currency(badal_amount, currency_type)
        badal_text = number_to_arabic_text(badal_amount, currency_type)
        mony_formatted = format_number_with_currency(mony_amount, currency_type)
        mony_text = number_to_arabic_text(mony_amount, currency_type)
        remaining_formatted = format_number_with_currency(remaining_amount, currency_type)
        remaining_text = number_to_arabic_text(remaining_amount, currency_type)

        # إعداد البيانات للمستند (نفس الطريقة القديمة)
        data = {
            'date': now.strftime('%Y-%m-%d'),
            'date_1': now.strftime('%Y-%m-%d'),
            't': request.form.get('t', ''),
            't_1': request.form.get('t_1', ''),
            't_11': request.form.get('t_1', ''),
            't_': request.form.get('t', ''),
            'day': request.form.get('day', ''),
            'serial_number': serial_number,
            'name_1': request.form.get('name_1', ''),
            'location_1': request.form.get('location_1', ''),
            'name_2': request.form.get('name_2', ''),
            'name_22': request.form.get('name_2', ''),
            'location_2': request.form.get('location_2', ''),
            'id_1': request.form.get('id_1', ''),
            'phone_1': request.form.get('phone_1', ''),
            'name_3': request.form.get('name_3', ''),
            'name_33': request.form.get('name_3', ''),
            'location_3': request.form.get('location_3', ''),
            'id_2': request.form.get('id_2', ''),
            'phone_2': request.form.get('phone_2', ''),
            'sin_num': request.form.get('sin_num', ''),
            'car_num': request.form.get('car_num', ''),
            'car_prop': request.form.get('car_prop', ''),
            'car_type': request.form.get('car_type', ''),
            'car_model': request.form.get('car_model', ''),
            'car_colar': request.form.get('car_colar', ''),
            'sasi_num': request.form.get('sasi_num', ''),
            'badal_num': badal_formatted,
            'badal_writing': badal_text,
            'mony_num': mony_formatted,
            'mony_writing': mony_text,
            'mony_not_delevired': remaining_formatted,
            'mony_not_delevired_writing': remaining_text,
            'note_a': request.form.get('note_a', ''),
            'note_b': request.form.get('note_b', ''),
            'm1': request.form.get('m1', ''),
            'car_city': request.form.get('car_city', ''),
            'currency_type': currency_type,
        }

        # استخراج الصور Base64 من الحقول
        seller_photo_base64 = request.form.get('sellerPhoto')
        buyer_photo_base64 = request.form.get('buyerPhoto')

        # حفظ الصور إذا كانت موجودة
        seller_photo_path = None
        buyer_photo_path = None
        if seller_photo_base64:
            seller_photo_path = save_photo(seller_photo_base64, 'seller')

        if buyer_photo_base64:
            buyer_photo_path = save_photo(buyer_photo_base64, 'buyer')

        # إنشاء معرف العقد أولاً قبل معالجة PDF
        contract_id = f"contract_{serial_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # إنشاء UUID للعقد لاستخدامه في QR code
        contract_uuid = str(uuid.uuid4())

        # توليد QR code للعقد
        qr_code_path = generate_qr_code(contract_uuid, serial_number)

        # إضافة UUID إلى بيانات العقد
        data['uuid'] = contract_uuid

        # إنشاء ملف Word باستخدام template.docx
        contract_filename = f'contract_{computer_name}_{serial_number}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx'
        contract_path = os.path.join(UPLOAD_FOLDER, contract_filename)
        print(f"📄 إنشاء ملف Word: {contract_filename}")

        # تعبئة الملف بالبيانات مع QR code
        fill_word_template('template.docx', contract_path, data, seller_photo_path=seller_photo_path, buyer_photo_path=buyer_photo_path, qr_code_path=qr_code_path)
        print(f"✅ تم إنشاء ملف Word بنجاح: {contract_path}")

        # تحويل Word إلى PDF باستخدام Gradio API حصرياً
        pdf_filename = contract_filename.replace('.docx', '.pdf')
        pdf_path = os.path.join(UPLOAD_FOLDER, pdf_filename)
        print(f"🔄 إرسال ملف Word إلى Gradio API للتحويل: {pdf_filename}")

        # معالجة PDF باستخدام Gradio API
        pdf_converted = False
        pdf_error_message = ""

        try:
            pdf_result = process_pdf_with_gradio(contract_path, pdf_path, contract_id)

            if pdf_result and pdf_result.get('success'):
                print(f"✅ تم تحويل Word إلى PDF بنجاح عبر Gradio API")
                pdf_converted = True
            else:
                error_msg = pdf_result.get('error', 'خطأ غير معروف في Gradio API') if pdf_result else 'لم يتم الحصول على استجابة من Gradio API'
                print(f"❌ فشل في تحويل Word إلى PDF عبر Gradio API: {error_msg}")
                pdf_error_message = error_msg

        except Exception as gradio_error:
            print(f"❌ خطأ في Gradio API: {gradio_error}")
            pdf_error_message = f"خطأ في Gradio API: {str(gradio_error)}"

        # حفظ بيانات العقد في Firebase

        # حفظ الصور
        images = {}
        if seller_photo_base64:
            images['sellerPhoto'] = seller_photo_base64
        if buyer_photo_base64:
            images['buyerPhoto'] = buyer_photo_base64

        # حفظ صورة QR Code كـ base64
        qr_code_base64 = None
        if qr_code_path and os.path.exists(qr_code_path):
            try:
                with open(qr_code_path, 'rb') as qr_file:
                    qr_code_base64 = base64.b64encode(qr_file.read()).decode('utf-8')
                print(f"✅ تم تحويل QR Code إلى base64 بنجاح")
            except Exception as e:
                print(f"❌ خطأ في تحويل QR Code إلى base64: {e}")

        # إضافة QR Code إلى الصور
        if qr_code_base64:
            images['qrCode'] = qr_code_base64

        firebase_path = f"finalized_contracts/{contract_id}"
        contract_with_images = {
            'contract_data': data,
            'images': images,
            'status': 'finalized',
            'finalized_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'word_filename': contract_filename,
            'pdf_filename': pdf_filename if pdf_converted else None,  # حفظ اسم PDF فقط إذا تم التحويل بنجاح
            'pdf_converted': pdf_converted,  # إضافة حالة التحويل
            'uuid': contract_uuid  # إضافة UUID للعقد
        }

        from firebase_manager import firebase_manager
        success = firebase_manager.set_data(firebase_path, contract_with_images)

        # حذف الصور المؤقتة
        if seller_photo_path and os.path.exists(seller_photo_path):
            try:
                os.remove(seller_photo_path)
            except Exception as e:
                print(f"خطأ في حذف صورة البائع: {e}")

        if buyer_photo_path and os.path.exists(buyer_photo_path):
            try:
                os.remove(buyer_photo_path)
            except Exception as e:
                print(f"خطأ في حذف صورة المشتري: {e}")

        # حذف QR code المؤقت
        if qr_code_path and os.path.exists(qr_code_path):
            try:
                os.remove(qr_code_path)
                print(f"تم حذف QR code: {qr_code_path}")
            except Exception as e:
                print(f"خطأ في حذف QR code: {e}")

        # حذف فوري لجميع الملفات المؤقتة المتعلقة بهذا العقد
        immediate_cleanup_contract_files(computer_id, contract_id)

        if success:
            print(f"✅ تم إبرام العقد بنجاح:")
            print(f"   - معرف العقد: {contract_id}")
            print(f"   - الرقم التسلسلي: {serial_number}")
            print(f"   - ملف Word: {contract_filename}")
            print(f"   - ملف PDF: {pdf_filename if pdf_converted else 'لم يتم التحويل'}")
            print(f"   - حالة تحويل PDF: {'نجح' if pdf_converted else 'فشل'}")

            if pdf_converted:
                # جدولة تنظيف ملفات المستخدم بعد إبرام العقد بنجاح
                cleanup_user_files_after_contract(computer_id, contract_id)

                # نجح تحويل PDF - إرسال رابط لعرض PDF مباشرة في المتصفح
                return {
                    'success': True,
                    'contract_id': contract_id,
                    'serial_number': serial_number,
                    'pdf_filename': pdf_filename,
                    'pdf_url': f'/pdf/{pdf_filename}',  # رابط لعرض PDF مباشرة
                    'pdf_converted': True,
                    'message': 'تم إبرام العقد وتحويله إلى PDF بنجاح عبر Gradio API'
                }
            else:
                # جدولة تنظيف ملفات المستخدم حتى في حالة فشل تحويل PDF
                cleanup_user_files_after_contract(computer_id, contract_id)

                # فشل تحويل PDF - إرسال رسالة خطأ مع توجيه للتعديل
                return {
                    'success': False,
                    'contract_id': contract_id,
                    'serial_number': serial_number,
                    'pdf_converted': False,
                    'error': 'تم حفظ العقد ولكن فشل في تحويله إلى PDF',
                    'redirect_to_edit': True,
                    'error_details': pdf_error_message or 'خطأ غير معروف في تحويل PDF'
                }
        else:
            print(f"❌ فشل في حفظ العقد في Firebase")
            return {'error': 'فشل في حفظ العقد في قاعدة البيانات'}, 500

    except Exception as e:
        print(f"❌ خطأ غير متوقع في إبرام العقد: {e}")
        import traceback
        traceback.print_exc()

        # تحديد نوع الخطأ وإرجاع رسالة مناسبة
        error_message = 'خطأ غير متوقع في إبرام العقد'
        if 'Firebase' in str(e):
            error_message = 'خطأ في الاتصال بقاعدة البيانات'
        elif 'template' in str(e).lower():
            error_message = 'خطأ في قالب العقد'
        elif 'photo' in str(e).lower() or 'image' in str(e).lower():
            error_message = 'خطأ في معالجة الصور'
        elif 'pdf' in str(e).lower():
            error_message = 'خطأ في تحويل العقد إلى PDF'
        else:
            error_message = f'خطأ في إبرام العقد: {str(e)}'

        return {'error': error_message}, 500

# API للإداريين - جلب قائمة جميع العقود
@app.route('/admin/api/contracts')
def admin_get_contracts():
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    contracts = []
    computers = load_computers()

    for computer_name, computer_data in computers.items():
        if computer_data.get('has_saved_contract', False):
            contract = load_saved_contract(computer_name)
            if contract:
                contracts.append({
                    'computer_name': computer_name,
                    'computer_id': computer_data['id'],
                    'contract_info': contract['contract_info']
                })

    return {'success': True, 'contracts': contracts}

# API للإداريين - تحديث صلاحيات المستخدم
@app.route('/admin/api/permissions', methods=['POST'])
def admin_update_permissions():
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    data = request.json
    computer_name = data.get('computer_name')
    permissions = data.get('permissions')

    if update_user_permissions(computer_name, permissions):
        return {'success': True, 'message': 'تم تحديث الصلاحيات بنجاح'}
    else:
        return {'success': False, 'message': 'فشل في تحديث الصلاحيات'}

# API للإداريين - تحديث صلاحيات جميع المستخدمين
@app.route('/admin/api/permissions/bulk', methods=['POST'])
def admin_update_all_permissions():
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    data = request.json
    all_permissions = data.get('permissions', {})

    success_count = 0
    total_count = len(all_permissions)

    for computer_name, permissions in all_permissions.items():
        if update_user_permissions(computer_name, permissions):
            success_count += 1

    if success_count == total_count:
        return {'success': True, 'message': f'تم تحديث صلاحيات جميع المستخدمين ({success_count}/{total_count}) بنجاح'}
    else:
        return {'success': False, 'message': f'تم تحديث {success_count} من أصل {total_count} مستخدمين'}

# API للإداريين - تحديث كلمة مرور الحاسبة
@app.route('/admin/api/computer/password', methods=['POST'])
def admin_update_computer_password():
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    data = request.json
    computer_name = data.get('computer_name')
    new_password = data.get('new_password', '').strip()

    if not computer_name or not new_password:
        return {'success': False, 'message': 'يرجى إدخال اسم الحاسبة وكلمة المرور الجديدة'}

    if update_computer_password(computer_name, new_password):
        return {'success': True, 'message': 'تم تحديث كلمة المرور بنجاح'}
    else:
        return {'success': False, 'message': 'فشل في تحديث كلمة المرور'}

# API للإداريين - جلب كلمة مرور الحاسبة
@app.route('/admin/api/computer/password/<computer_name>', methods=['GET'])
def admin_get_computer_password(computer_name):
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    password = get_computer_password(computer_name)
    if password is not None:
        return {'success': True, 'password': password}
    else:
        return {'success': False, 'message': 'الحاسبة غير موجودة'}

# API للإداريين - جلب كلمة مرور الإدارة الحالية
@app.route('/admin/api/admin/password', methods=['GET'])
def admin_get_admin_password():
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    try:
        admin_config = load_admin_config()
        return {'success': True, 'password': admin_config.get('password', '')}
    except Exception as e:
        return {'success': False, 'message': f'خطأ في جلب كلمة المرور: {str(e)}'}

# API لتحميل المستند المُنتج مع إمكانية الحفظ المحلي
@app.route('/api/download_document', methods=['POST'])
def download_document():
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    try:
        # الحصول على بيانات النموذج
        form_data = request.form.to_dict()

        # معالجة الصور
        buyer_photo_data = form_data.get('buyerPhoto', '')
        seller_photo_data = form_data.get('sellerPhoto', '')

        buyer_photo_path = None
        seller_photo_path = None

        # حفظ صورة البائع
        if buyer_photo_data:
            buyer_photo_path = os.path.join(UPLOAD_FOLDER, f'buyer_{session["computer_id"]}.jpg')
            with open(buyer_photo_path, 'wb') as f:
                f.write(base64.b64decode(buyer_photo_data))

        # حفظ صورة المشتري
        if seller_photo_data:
            seller_photo_path = os.path.join(UPLOAD_FOLDER, f'seller_{session["computer_id"]}.jpg')
            with open(seller_photo_path, 'wb') as f:
                f.write(base64.b64decode(seller_photo_data))

        # الحصول على رقم تسلسلي جديد (نفس منطق المسار الرئيسي)
        computer_id = session.get('computer_id')
        computer_name = session.get('computer_name')

        # التحقق من وضع التعديل
        is_editing_saved = form_data.get('is_editing_saved', 'false') == 'true'

        # التحقق من وجود رقم تسلسلي من ملف محلي
        local_serial_number = form_data.get('local_serial_number', '').strip()

        # التحقق من وجود رقم تسلسلي يدوي
        manual_serial_number = form_data.get('manual_serial_number', '').strip()

        if is_editing_saved:
            # استخدام الرقم التسلسلي من العقد المحفوظ
            saved_contract = load_saved_contract(computer_name)
            if saved_contract:
                serial_number = saved_contract['contract_info']['serial_number']
            else:
                # إذا لم يوجد عقد محفوظ، احصل على رقم جديد
                serial_number = get_and_increment_serial_number()
                if computer_name:
                    increment_user_contracts(computer_name)
        elif manual_serial_number:
            # استخدام الرقم التسلسلي اليدوي (لا نزيد العداد)
            try:
                manual_serial = int(manual_serial_number)
                if manual_serial > 0:
                    serial_number = manual_serial
                    print(f"🔢 استخدام الرقم التسلسلي اليدوي: {serial_number}")
                else:
                    # رقم غير صالح، احصل على رقم جديد
                    serial_number = get_and_increment_serial_number()
                    if computer_name:
                        increment_user_contracts(computer_name)
            except (ValueError, TypeError):
                # رقم غير صالح، احصل على رقم جديد
                serial_number = get_and_increment_serial_number()
                if computer_name:
                    increment_user_contracts(computer_name)
        elif local_serial_number:
            # استخدام الرقم التسلسلي من الملف المحلي (لا نزيد العداد)
            try:
                local_serial = int(local_serial_number)
                if local_serial > 0:
                    serial_number = local_serial
                    print(f"🔄 استخدام الرقم التسلسلي من الملف المحلي: {serial_number}")
                else:
                    # رقم غير صالح، احصل على رقم جديد
                    serial_number = get_and_increment_serial_number()
                    if computer_name:
                        increment_user_contracts(computer_name)
            except (ValueError, TypeError):
                # رقم غير صالح، احصل على رقم جديد
                serial_number = get_and_increment_serial_number()
                if computer_name:
                    increment_user_contracts(computer_name)
        else:
            # عقد جديد - احصل على رقم تسلسلي جديد
            serial_number = get_and_increment_serial_number()
            if computer_name:
                increment_user_contracts(computer_name)

        # إنشاء المستند
        output_path = f'filled_template_{session["computer_id"]}.docx'

        # تحضير البيانات للمستند (نفس منطق المسار الرئيسي)
        currency_type = form_data.get('currency_type', 'IQD')
        badal_num_raw = form_data.get('badal_num', '').replace(',', '')
        mony_num_raw = form_data.get('mony_num', '').replace(',', '')

        # حساب المبلغ المتبقي
        try:
            badal_amount = int(badal_num_raw) if badal_num_raw else 0
            mony_amount = int(mony_num_raw) if mony_num_raw else 0
            remaining_amount = badal_amount - mony_amount
        except ValueError:
            badal_amount = 0
            mony_amount = 0
            remaining_amount = 0

        # تنسيق المبالغ مع النص العربي
        badal_formatted = format_number_with_currency(badal_amount, currency_type)
        badal_text = number_to_arabic_text(badal_amount, currency_type)
        mony_formatted = format_number_with_currency(mony_amount, currency_type)
        mony_text = number_to_arabic_text(mony_amount, currency_type)
        remaining_formatted = format_number_with_currency(remaining_amount, currency_type)
        remaining_text = number_to_arabic_text(remaining_amount, currency_type)

        # إنشاء UUID للعقد لاستخدامه في QR code
        contract_uuid = str(uuid.uuid4())

        # توليد QR code للعقد
        qr_code_path = generate_qr_code(contract_uuid, serial_number)

        # إعداد البيانات للمستند
        document_data = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'date_1': datetime.now().strftime('%Y-%m-%d'),
            't': form_data.get('t', ''),
            't_1': form_data.get('t_1', ''),
            't_11': form_data.get('t_1', ''),
            't_': form_data.get('t', ''),
            'day': form_data.get('day', ''),
            'name_1': form_data.get('name_1', ''),
            'location_1': form_data.get('location_1', ''),
            'name_2': form_data.get('name_2', ''),
            'name_22': form_data.get('name_2', ''),  # نسخة إضافية للبائع
            'location_2': form_data.get('location_2', ''),
            'id_1': form_data.get('id_1', ''),
            'phone_1': form_data.get('phone_1', ''),
            'name_3': form_data.get('name_3', ''),
            'name_33': form_data.get('name_3', ''),  # نسخة إضافية للمشتري
            'location_3': form_data.get('location_3', ''),
            'id_2': form_data.get('id_2', ''),
            'phone_2': form_data.get('phone_2', ''),
            'car_type': form_data.get('car_type', ''),
            'car_num': form_data.get('car_num', ''),
            'car_prop': form_data.get('car_prop', ''),
            'sin_num': form_data.get('sin_num', ''),
            'car_colar': form_data.get('car_colar', ''),
            'car_model': form_data.get('car_model', ''),
            'sasi_num': form_data.get('sasi_num', ''),
            'badal_num': badal_formatted,
            'badal_writing': badal_text,
            'mony_num': mony_formatted,
            'mony_writing': mony_text,
            'mony_not_delevired': remaining_formatted,
            'mony_not_delevired_writing': remaining_text,
            'note_a': form_data.get('note_a', ''),
            'note_b': form_data.get('note_b', ''),
            'm1': form_data.get('m1', ''),
            'car_city': form_data.get('car_city', ''),
            'serial_number': serial_number,
            'currency_type': currency_type,
            'uuid': contract_uuid
        }

        # إنشاء المستند مع QR code
        fill_word_template('template.docx', output_path, document_data, seller_photo_path, buyer_photo_path, qr_code_path)

        # إنشاء اسم الملف للتحميل
        seller_name = form_data.get('name_2', 'غير_محدد').replace(' ', '_')
        download_filename = f'contract_{seller_name}_{serial_number}.docx'

        # استخدام الدالة المخصصة للإرسال مع الحذف التلقائي
        # سيتم حذف جميع الملفات المؤقتة تلقائياً بعد الإرسال
        return send_file_with_auto_cleanup(output_path, download_filename, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')

    except Exception as e:
        return {'error': f'خطأ في إنشاء المستند: {str(e)}'}, 500

# API لحفظ المستند والمسودة معاً في مجلد محلي
@app.route('/api/save_document_and_draft', methods=['POST'])
def save_document_and_draft():
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    try:
        # الحصول على بيانات النموذج
        form_data = request.form.to_dict()
        computer_id = session.get('computer_id')
        computer_name = session.get('computer_name', '')

        # معالجة الصور
        buyer_photo_data = form_data.get('buyerPhoto', '')
        seller_photo_data = form_data.get('sellerPhoto', '')

        buyer_photo_path = None
        seller_photo_path = None

        # حفظ صورة البائع
        if buyer_photo_data:
            buyer_photo_path = os.path.join(UPLOAD_FOLDER, f'buyer_{computer_id}.jpg')
            with open(buyer_photo_path, 'wb') as f:
                f.write(base64.b64decode(buyer_photo_data))

        # حفظ صورة المشتري
        if seller_photo_data:
            seller_photo_path = os.path.join(UPLOAD_FOLDER, f'seller_{computer_id}.jpg')
            with open(seller_photo_path, 'wb') as f:
                f.write(base64.b64decode(seller_photo_data))

        # الحصول على رقم تسلسلي جديد
        is_editing_saved = form_data.get('is_editing_saved', 'false') == 'true'

        # التحقق من وجود رقم تسلسلي من ملف محلي
        local_serial_number = form_data.get('local_serial_number', '').strip()

        # التحقق من وجود رقم تسلسلي يدوي
        manual_serial_number = form_data.get('manual_serial_number', '').strip()

        if is_editing_saved:
            saved_contract = load_saved_contract(computer_name)
            if saved_contract:
                serial_number = saved_contract['contract_info']['serial_number']
            else:
                serial_number = get_and_increment_serial_number()
                if computer_name:
                    increment_user_contracts(computer_name)
        elif manual_serial_number:
            # استخدام الرقم التسلسلي اليدوي (لا نزيد العداد)
            try:
                manual_serial = int(manual_serial_number)
                if manual_serial > 0:
                    serial_number = manual_serial
                    print(f"🔢 استخدام الرقم التسلسلي اليدوي: {serial_number}")
                else:
                    # رقم غير صالح، احصل على رقم جديد
                    serial_number = get_and_increment_serial_number()
                    if computer_name:
                        increment_user_contracts(computer_name)
            except (ValueError, TypeError):
                # رقم غير صالح، احصل على رقم جديد
                serial_number = get_and_increment_serial_number()
                if computer_name:
                    increment_user_contracts(computer_name)
        elif local_serial_number:
            # استخدام الرقم التسلسلي من الملف المحلي (لا نزيد العداد)
            try:
                local_serial = int(local_serial_number)
                if local_serial > 0:
                    serial_number = local_serial
                    print(f"🔄 استخدام الرقم التسلسلي من الملف المحلي: {serial_number}")
                else:
                    # رقم غير صالح، احصل على رقم جديد
                    serial_number = get_and_increment_serial_number()
                    if computer_name:
                        increment_user_contracts(computer_name)
            except (ValueError, TypeError):
                # رقم غير صالح، احصل على رقم جديد
                serial_number = get_and_increment_serial_number()
                if computer_name:
                    increment_user_contracts(computer_name)
        else:
            serial_number = get_and_increment_serial_number()
            if computer_name:
                increment_user_contracts(computer_name)

        # تحضير البيانات للمستند
        currency_type = form_data.get('currency_type', 'IQD')
        badal_num_raw = form_data.get('badal_num', '').replace(',', '')
        mony_num_raw = form_data.get('mony_num', '').replace(',', '')

        # حساب المبلغ المتبقي
        try:
            badal_amount = int(badal_num_raw) if badal_num_raw else 0
            mony_amount = int(mony_num_raw) if mony_num_raw else 0
            remaining_amount = badal_amount - mony_amount
        except ValueError:
            badal_amount = 0
            mony_amount = 0
            remaining_amount = 0

        # تنسيق المبالغ مع النص العربي
        badal_formatted = format_number_with_currency(badal_amount, currency_type)
        badal_text = number_to_arabic_text(badal_amount, currency_type)
        mony_formatted = format_number_with_currency(mony_amount, currency_type)
        mony_text = number_to_arabic_text(mony_amount, currency_type)
        remaining_formatted = format_number_with_currency(remaining_amount, currency_type)
        remaining_text = number_to_arabic_text(remaining_amount, currency_type)

        # إنشاء UUID للعقد لاستخدامه في QR code
        contract_uuid = str(uuid.uuid4())

        # توليد QR code للعقد
        qr_code_path = generate_qr_code(contract_uuid, serial_number)

        # إعداد البيانات للمستند
        document_data = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'date_1': datetime.now().strftime('%Y-%m-%d'),
            't': form_data.get('t', ''),
            't_1': form_data.get('t_1', ''),
            't_11': form_data.get('t_1', ''),
            't_': form_data.get('t', ''),
            'day': form_data.get('day', ''),
            'name_1': form_data.get('name_1', ''),
            'location_1': form_data.get('location_1', ''),
            'name_2': form_data.get('name_2', ''),
            'name_22': form_data.get('name_2', ''),  # نسخة إضافية للبائع
            'location_2': form_data.get('location_2', ''),
            'id_1': form_data.get('id_1', ''),
            'phone_1': form_data.get('phone_1', ''),
            'name_3': form_data.get('name_3', ''),
            'name_33': form_data.get('name_3', ''),  # نسخة إضافية للمشتري
            'location_3': form_data.get('location_3', ''),
            'id_2': form_data.get('id_2', ''),
            'phone_2': form_data.get('phone_2', ''),
            'car_type': form_data.get('car_type', ''),
            'car_num': form_data.get('car_num', ''),
            'car_prop': form_data.get('car_prop', ''),
            'sin_num': form_data.get('sin_num', ''),
            'car_colar': form_data.get('car_colar', ''),
            'car_model': form_data.get('car_model', ''),
            'sasi_num': form_data.get('sasi_num', ''),
            'badal_num': badal_formatted,
            'badal_writing': badal_text,
            'mony_num': mony_formatted,
            'mony_writing': mony_text,
            'mony_not_delevired': remaining_formatted,
            'mony_not_delevired_writing': remaining_text,
            'note_a': form_data.get('note_a', ''),
            'note_b': form_data.get('note_b', ''),
            'm1': form_data.get('m1', ''),
            'car_city': form_data.get('car_city', ''),
            'serial_number': serial_number,
            'currency_type': currency_type,
            'uuid': contract_uuid
        }

        # إنشاء المستند مع QR code
        output_path = f'filled_template_{computer_id}.docx'
        fill_word_template('template.docx', output_path, document_data, seller_photo_path, buyer_photo_path, qr_code_path)

        # إنشاء ملف JSON للمسودة
        draft_data = {
            'version': '1.0',
            'created_at': datetime.now().isoformat(),
            'computer_info': {
                'computer_name': computer_name,
                'computer_id': computer_id
            },
            'form_data': {k: v for k, v in form_data.items() if k not in ['buyerPhoto', 'sellerPhoto']},
            'images': {
                'buyerPhoto': buyer_photo_data if buyer_photo_data else None,
                'sellerPhoto': seller_photo_data if seller_photo_data else None
            },
            'contract_info': {
                'serial_number': serial_number,
                'seller_name': form_data.get('name_2', 'غير_محدد'),
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            'metadata': {
                'total_fields': len(form_data),
                'filled_fields': len([v for v in form_data.values() if v and v.strip()]),
                'has_buyer_photo': bool(buyer_photo_data),
                'has_seller_photo': bool(seller_photo_data)
            }
        }

        # إنشاء أسماء الملفات
        seller_name = form_data.get('name_2', 'غير_محدد').replace(' ', '_')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        word_filename = f'contract_{seller_name}_{serial_number}.docx'
        json_filename = f'draft_{seller_name}_{serial_number}_{timestamp}.json'

        # إنشاء ملف JSON مؤقت
        json_temp_path = os.path.join(UPLOAD_FOLDER, json_filename)
        with open(json_temp_path, 'w', encoding='utf-8') as f:
            json.dump(draft_data, f, ensure_ascii=False, indent=2)

        # إنشاء نسخة من ملف Word في مجلد uploads للتحميل المباشر
        word_temp_path = os.path.join(UPLOAD_FOLDER, word_filename)
        import shutil
        shutil.copy2(output_path, word_temp_path)

        # إرسال الملفات كـ ZIP
        import zipfile
        zip_filename = f'contract_package_{seller_name}_{serial_number}.zip'
        zip_path = os.path.join(UPLOAD_FOLDER, zip_filename)

        with zipfile.ZipFile(zip_path, 'w') as zipf:
            zipf.write(output_path, word_filename)
            zipf.write(json_temp_path, json_filename)

        # حذف الملفات المؤقتة
        def cleanup_files():
            try:
                if buyer_photo_path and os.path.exists(buyer_photo_path):
                    os.remove(buyer_photo_path)
                if seller_photo_path and os.path.exists(seller_photo_path):
                    os.remove(seller_photo_path)
                if qr_code_path and os.path.exists(qr_code_path):
                    os.remove(qr_code_path)
                if os.path.exists(output_path):
                    os.remove(output_path)
                if os.path.exists(json_temp_path):
                    os.remove(json_temp_path)
                if os.path.exists(word_temp_path):
                    os.remove(word_temp_path)
                if os.path.exists(zip_path):
                    os.remove(zip_path)
            except:
                pass

        # حذف الملفات فوراً بعد الإرسال مع تأخير بسيط
        threading.Timer(10.0, cleanup_files).start()

        # حفظ العقد كمسودة تلقائياً (مع الصور)
        contract_data = {k: v for k, v in document_data.items() if k not in ['seller_photo', 'buyer_photo']}
        contract_data['serial_number'] = serial_number
        contract_data['uuid'] = contract_uuid  # إضافة UUID للمسودة

        if not is_editing_saved:
            contract_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # تحضير الصور للحفظ
        images = {}
        if buyer_photo_data:
            images['buyerPhoto'] = buyer_photo_data
        if seller_photo_data:
            images['sellerPhoto'] = seller_photo_data

        # حفظ صورة QR Code كـ base64
        qr_code_base64 = None
        if qr_code_path and os.path.exists(qr_code_path):
            try:
                with open(qr_code_path, 'rb') as qr_file:
                    qr_code_base64 = base64.b64encode(qr_file.read()).decode('utf-8')
                print(f"✅ تم تحويل QR Code إلى base64 بنجاح للمسودة")
            except Exception as e:
                print(f"❌ خطأ في تحويل QR Code إلى base64 للمسودة: {e}")

        # إضافة QR Code إلى الصور
        if qr_code_base64:
            images['qrCode'] = qr_code_base64

        save_contract_data(computer_id, contract_data, is_editing_saved, images)

        return {
            'success': True,
            'word_filename': word_filename,
            'json_filename': json_filename,
            'seller_name': seller_name,
            'serial_number': serial_number,
            'zip_download_url': f'/download_temp_file/{zip_filename}',
            'word_download_url': f'/download_temp_file/{word_filename}'
        }

    except Exception as e:
        return {'error': f'خطأ في إنشاء الملفات: {str(e)}'}, 500

# مسار لتحميل الملفات المؤقتة
@app.route('/download_temp_file/<filename>')
def download_temp_file(filename):
    try:
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        if os.path.exists(file_path):
            # استخدام الدالة المخصصة للإرسال مع الحذف التلقائي
            return send_file_with_auto_cleanup(file_path, filename)
        else:
            return {'error': 'الملف غير موجود'}, 404
    except Exception as e:
        return {'error': f'خطأ في تحميل الملف: {str(e)}'}, 500

# ملاحظة: تم نقل دالة print_contract إلى مكان آخر في الكود

# مسار مباشر لعرض ملفات PDF من مجلد uploads
@app.route('/pdf/<filename>')
def serve_pdf(filename):
    if 'computer_id' not in session:
        return redirect(url_for('login'))

    try:
        # التأكد من أن الملف هو PDF
        if not filename.endswith('.pdf'):
            return "ملف غير صالح", 400

        pdf_path = os.path.join(UPLOAD_FOLDER, filename)
        if os.path.exists(pdf_path):
            print(f"📤 عرض ملف PDF: {pdf_path}")

            # إرسال الملف مع جدولة الحذف الفوري بعد العرض
            response = send_file(pdf_path, mimetype='application/pdf')

            # جدولة حذف الملف فوراً بعد العرض مع تأخير بسيط
            def immediate_pdf_cleanup():
                time.sleep(5)  # تأخير بسيط (5 ثواني) لضمان اكتمال العرض
                try:
                    if os.path.exists(pdf_path):
                        os.remove(pdf_path)
                        print(f"🗑️ تم حذف ملف PDF بعد العرض: {filename}")

                        # تنظيف إضافي لمجلد uploads
                        cleanup_uploads_folder()
                except Exception as e:
                    print(f"⚠️ خطأ في حذف ملف PDF بعد العرض: {e}")

            # تشغيل الحذف في خيط منفصل
            cleanup_thread = threading.Thread(target=immediate_pdf_cleanup, daemon=True)
            cleanup_thread.start()

            return response
        else:
            print(f"❌ ملف PDF غير موجود: {pdf_path}")
            return "الملف غير موجود", 404

    except Exception as e:
        print(f"❌ خطأ في serve_pdf: {str(e)}")
        return f"خطأ في عرض الملف: {str(e)}", 500

# صفحة إدارة العقود - متاحة للجميع
@app.route('/contracts')
def contracts_management():
    if 'computer_id' not in session:
        return redirect(url_for('login'))

    try:
        from firebase_manager import firebase_manager

        # جلب جميع العقود المبرمة
        contracts = firebase_manager.get_data("finalized_contracts")

        if contracts:
            # تحويل البيانات إلى قائمة وترتيبها حسب التاريخ
            contracts_list = []
            for contract_id, contract_data in contracts.items():
                contract_info = contract_data.get('contract_data', {})
                contracts_list.append({
                    'id': contract_id,
                    'serial_number': contract_info.get('serial_number', ''),
                    'date': contract_info.get('date', contract_info.get('date_1', '')),
                    'seller_name': contract_info.get('name_2', ''),
                    'seller_phone': contract_info.get('phone_1', ''),
                    'buyer_name': contract_info.get('name_3', ''),
                    'buyer_phone': contract_info.get('phone_2', ''),
                    'car_type': contract_info.get('car_type', ''),
                    'car_number': contract_info.get('car_num', ''),
                    'finalized_at': contract_data.get('finalized_at', '')
                })

            # ترتيب حسب التاريخ (الأحدث أولاً)
            contracts_list.sort(key=lambda x: x['finalized_at'], reverse=True)
        else:
            contracts_list = []

        # التحقق من صلاحيات المستخدم
        computer_name = session.get('computer_name')
        is_admin_user = is_admin(session)

        user_permissions = {
            'can_edit': is_admin_user or check_permission(computer_name, 'edit_finalized_contracts'),
            'can_download': is_admin_user or check_permission(computer_name, 'download_finalized_contracts'),
            'is_admin': is_admin_user
        }

        return render_template('contracts_management.html',
                             contracts=contracts_list,
                             user_permissions=user_permissions)
    except Exception as e:
        return f"خطأ في تحميل العقود: {str(e)}", 500

# API لتحميل العقد كـ Word - متاح للجميع
@app.route('/api/download_contract_word/<contract_id>')
def download_contract_word(contract_id):
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    try:
        from firebase_manager import firebase_manager

        # جلب بيانات العقد من Firebase
        contract_data = firebase_manager.get_data(f"finalized_contracts/{contract_id}")

        if not contract_data:
            return "العقد غير موجود", 404

        # التحقق من وجود ملف Word المحفوظ
        word_filename = contract_data.get('word_filename')
        if word_filename:
            word_path = os.path.join(UPLOAD_FOLDER, word_filename)
            if os.path.exists(word_path):
                # إرسال ملف Word المحفوظ مع الحذف التلقائي
                contract_info = contract_data['contract_data']
                filename = f"contract_{contract_info.get('name_2', 'unknown')}_{contract_info.get('serial_number', 'unknown')}.docx"
                return send_file_with_auto_cleanup(word_path, filename, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')

        # إذا لم يوجد ملف Word محفوظ، إنشاؤه من جديد
        contract_info = contract_data['contract_data']
        images = contract_data.get('images', {})

        # حفظ الصور مؤقتاً
        buyer_photo_path = None
        seller_photo_path = None

        if images.get('buyerPhoto'):
            buyer_photo_path = os.path.join(UPLOAD_FOLDER, f'temp_buyer_{contract_id}.jpg')
            with open(buyer_photo_path, 'wb') as f:
                f.write(base64.b64decode(images['buyerPhoto']))

        if images.get('sellerPhoto'):
            seller_photo_path = os.path.join(UPLOAD_FOLDER, f'temp_seller_{contract_id}.jpg')
            with open(seller_photo_path, 'wb') as f:
                f.write(base64.b64decode(images['sellerPhoto']))

        # استخدام QR code المحفوظ إذا كان متوفراً
        qr_code_path = None
        if images.get('qrCode'):
            try:
                qr_code_data = base64.b64decode(images['qrCode'])
                qr_code_path = os.path.join(UPLOAD_FOLDER, f'temp_qr_{contract_id}.png')
                with open(qr_code_path, 'wb') as f:
                    f.write(qr_code_data)
                print(f"✅ تم استخدام QR Code المحفوظ لتحميل العقد {contract_id}")
            except Exception as e:
                print(f"❌ خطأ في استخدام QR Code المحفوظ لتحميل العقد {contract_id}: {e}")
                # في حالة الفشل، توليد QR code جديد
                contract_uuid = contract_data.get('uuid')
                # البحث في form_data إذا لم نجد UUID في المستوى الأعلى
                if not contract_uuid:
                    saved_contract_data = contract_data.get('form_data', {})
                    contract_uuid = saved_contract_data.get('uuid')

                if contract_uuid:
                    qr_code_path = generate_qr_code(contract_uuid, contract_info.get('serial_number', contract_id))
                    if qr_code_path:
                        save_qr_code_to_firebase(contract_id, qr_code_path)
                        print(f"✅ تم توليد وحفظ QR Code جديد لتحميل العقد {contract_id}")
                else:
                    # إنشاء UUID جديد إذا لم يكن موجوداً
                    contract_uuid = str(uuid.uuid4())
                    qr_code_path = generate_qr_code(contract_uuid, contract_info.get('serial_number', contract_id))
                    if qr_code_path:
                        # حفظ UUID الجديد في Firebase
                        from firebase_manager import firebase_manager
                        firebase_manager.set_data(f"finalized_contracts/{contract_id}/uuid", contract_uuid)
                        save_qr_code_to_firebase(contract_id, qr_code_path)
                        print(f"✅ تم إنشاء UUID جديد وحفظه لتحميل العقد {contract_id}: {contract_uuid}")
        else:
            # إذا لم يكن QR Code محفوظاً، توليد جديد
            contract_uuid = contract_data.get('uuid')
            # البحث في form_data إذا لم نجد UUID في المستوى الأعلى
            if not contract_uuid:
                saved_contract_data = contract_data.get('form_data', {})
                contract_uuid = saved_contract_data.get('uuid')

            if not contract_uuid:
                # إنشاء UUID جديد إذا لم يكن موجوداً
                contract_uuid = str(uuid.uuid4())
                from firebase_manager import firebase_manager
                firebase_manager.set_data(f"finalized_contracts/{contract_id}/uuid", contract_uuid)
                print(f"✅ تم إنشاء UUID جديد للعقد {contract_id}: {contract_uuid}")

            qr_code_path = generate_qr_code(contract_uuid, contract_info.get('serial_number', contract_id))
            if qr_code_path:
                save_qr_code_to_firebase(contract_id, qr_code_path)
                print(f"✅ تم توليد وحفظ QR Code جديد لتحميل العقد {contract_id}")

        # إنشاء ملف Word مع QR code
        output_path = os.path.join(UPLOAD_FOLDER, f'contract_{contract_id}.docx')
        fill_word_template('template.docx', output_path, contract_info, seller_photo_path, buyer_photo_path, qr_code_path)

        # إرسال الملف للتحميل مع الحذف التلقائي
        # سيتم حذف جميع الملفات المؤقتة تلقائياً بعد الإرسال
        filename = f"contract_{contract_info.get('name_2', 'unknown')}_{contract_info.get('serial_number', 'unknown')}.docx"
        return send_file_with_auto_cleanup(output_path, filename, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')

    except Exception as e:
        return f"خطأ في تحميل العقد: {str(e)}", 500

# API لإنشاء PDF مؤقت للمعاينة من بيانات edit_contract
@app.route('/api/preview_contract_pdf', methods=['POST'])
def preview_contract_pdf():
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    try:
        computer_id = session.get('computer_id')
        computer_name = session.get('computer_name')

        # جمع البيانات من النموذج
        form_data = request.form.to_dict()

        # معالجة الصور
        seller_photo_base64 = form_data.get('sellerPhoto', '')
        buyer_photo_base64 = form_data.get('buyerPhoto', '')

        seller_photo_path = None
        buyer_photo_path = None

        # حفظ الصور مؤقتاً للمعاينة
        if seller_photo_base64:
            seller_photo_path = os.path.join(UPLOAD_FOLDER, f'preview_seller_{computer_id}_{int(time.time())}.jpg')
            try:
                with open(seller_photo_path, 'wb') as f:
                    f.write(base64.b64decode(seller_photo_base64))
            except Exception as e:
                print(f"خطأ في حفظ صورة البائع للمعاينة: {e}")
                seller_photo_path = None

        if buyer_photo_base64:
            buyer_photo_path = os.path.join(UPLOAD_FOLDER, f'preview_buyer_{computer_id}_{int(time.time())}.jpg')
            try:
                with open(buyer_photo_path, 'wb') as f:
                    f.write(base64.b64decode(buyer_photo_base64))
            except Exception as e:
                print(f"خطأ في حفظ صورة المشتري للمعاينة: {e}")
                buyer_photo_path = None

        # تحضير البيانات للمستند
        currency_type = form_data.get('currency_type', 'IQD')
        badal_num_raw = form_data.get('badal_num', '').replace(',', '')
        mony_num_raw = form_data.get('mony_num', '').replace(',', '')

        # حساب المبلغ المتبقي
        try:
            badal_amount = int(badal_num_raw) if badal_num_raw else 0
            mony_amount = int(mony_num_raw) if mony_num_raw else 0
            remaining_amount = badal_amount - mony_amount
        except ValueError:
            badal_amount = 0
            mony_amount = 0
            remaining_amount = 0

        # تنسيق المبالغ مع النص العربي
        badal_formatted = format_number_with_currency(badal_amount, currency_type)
        badal_text = number_to_arabic_text(badal_amount, currency_type)
        mony_formatted = format_number_with_currency(mony_amount, currency_type)
        mony_text = number_to_arabic_text(mony_amount, currency_type)
        remaining_formatted = format_number_with_currency(remaining_amount, currency_type)
        remaining_text = number_to_arabic_text(remaining_amount, currency_type)

        # إنشاء UUID للعقد لاستخدامه في QR code للمعاينة
        contract_uuid = str(uuid.uuid4())

        # توليد QR code للعقد
        qr_code_path = generate_qr_code(contract_uuid, form_data.get('serial_number', ''))

        # إعداد البيانات للمستند
        document_data = {
            'date': form_data.get('date', datetime.now().strftime('%Y-%m-%d')),
            'date_1': form_data.get('date', datetime.now().strftime('%Y-%m-%d')),
            't': form_data.get('t', ''),
            't_1': form_data.get('t_1', ''),
            't_11': form_data.get('t_1', ''),
            't_': form_data.get('t', ''),
            'day': form_data.get('day', ''),
            'name_1': form_data.get('name_1', ''),
            'location_1': form_data.get('location_1', ''),
            'name_2': form_data.get('name_2', ''),
            'name_22': form_data.get('name_2', ''),
            'location_2': form_data.get('location_2', ''),
            'id_1': form_data.get('id_1', ''),
            'phone_1': form_data.get('phone_1', ''),
            'name_3': form_data.get('name_3', ''),
            'name_33': form_data.get('name_3', ''),
            'location_3': form_data.get('location_3', ''),
            'id_2': form_data.get('id_2', ''),
            'phone_2': form_data.get('phone_2', ''),
            'car_type': form_data.get('car_type', ''),
            'car_num': form_data.get('car_num', ''),
            'car_prop': form_data.get('car_prop', ''),
            'sin_num': form_data.get('sin_num', ''),
            'car_colar': form_data.get('car_colar', ''),
            'car_model': form_data.get('car_model', ''),
            'sasi_num': form_data.get('sasi_num', ''),
            'badal_num': badal_formatted,
            'badal_writing': badal_text,
            'mony_num': mony_formatted,
            'mony_writing': mony_text,
            'mony_not_delevired': remaining_formatted,
            'mony_not_delevired_writing': remaining_text,
            'note_a': form_data.get('note_a', ''),
            'note_b': form_data.get('note_b', ''),
            'm1': form_data.get('m1', ''),
            'car_city': form_data.get('car_city', ''),
            'serial_number': form_data.get('serial_number', ''),
            'currency_type': currency_type,
            'uuid': contract_uuid
        }

        # إنشاء ملف Word مؤقت للمعاينة
        timestamp = int(time.time())
        preview_word_filename = f'preview_contract_{computer_id}_{timestamp}.docx'
        preview_word_path = os.path.join(UPLOAD_FOLDER, preview_word_filename)

        # تعبئة الملف بالبيانات مع QR code
        fill_word_template('template.docx', preview_word_path, document_data, seller_photo_path, buyer_photo_path, qr_code_path)

        # تحويل Word إلى PDF للمعاينة
        preview_pdf_filename = f'preview_contract_{computer_id}_{timestamp}.pdf'
        preview_pdf_path = os.path.join(UPLOAD_FOLDER, preview_pdf_filename)

        # محاولة تحويل PDF عبر Gradio API
        pdf_converted = False
        try:
            pdf_converted = convert_word_to_pdf_via_gradio(preview_word_path, preview_pdf_path)
        except Exception as e:
            print(f"خطأ في تحويل PDF للمعاينة عبر Gradio API: {e}")

        # حذف الصور المؤقتة فوراً
        if seller_photo_path and os.path.exists(seller_photo_path):
            try:
                os.remove(seller_photo_path)
            except:
                pass

        if buyer_photo_path and os.path.exists(buyer_photo_path):
            try:
                os.remove(buyer_photo_path)
            except:
                pass

        # حذف QR code المؤقت
        if qr_code_path and os.path.exists(qr_code_path):
            try:
                os.remove(qr_code_path)
            except:
                pass

        # حذف ملف Word المؤقت
        if os.path.exists(preview_word_path):
            try:
                os.remove(preview_word_path)
            except:
                pass

        if pdf_converted and os.path.exists(preview_pdf_path):
            # جدولة حذف ملف PDF بعد 5 دقائق
            def cleanup_preview_pdf():
                try:
                    if os.path.exists(preview_pdf_path):
                        os.remove(preview_pdf_path)
                        print(f"تم حذف ملف المعاينة: {preview_pdf_path}")
                except:
                    pass

            threading.Timer(300.0, cleanup_preview_pdf).start()  # 5 دقائق

            return {
                'success': True,
                'pdf_filename': preview_pdf_filename,
                'message': 'تم إنشاء PDF للمعاينة بنجاح'
            }
        else:
            return {
                'success': False,
                'error': 'فشل في تحويل العقد إلى PDF للمعاينة'
            }

    except Exception as e:
        print(f"❌ خطأ غير متوقع في إنشاء PDF للمعاينة: {e}")
        import traceback
        traceback.print_exc()

        # تحديد نوع الخطأ وإرجاع رسالة مناسبة
        error_message = 'خطأ غير متوقع في إنشاء PDF للمعاينة'
        if 'template' in str(e).lower():
            error_message = 'خطأ في قالب العقد'
        elif 'photo' in str(e).lower() or 'image' in str(e).lower():
            error_message = 'خطأ في معالجة الصور'
        elif 'pdf' in str(e).lower():
            error_message = 'خطأ في تحويل العقد إلى PDF'
        elif 'permission' in str(e).lower() or 'access' in str(e).lower():
            error_message = 'خطأ في صلاحيات الملفات'
        else:
            error_message = f'خطأ في إنشاء PDF للمعاينة: {str(e)}'

        return {'error': error_message}, 500

# API لجلب بيانات العقد للتعديل
@app.route('/api/get_contract/<contract_id>')
def get_contract_for_edit(contract_id):
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    try:
        from firebase_manager import firebase_manager

        # جلب بيانات العقد من Firebase
        contract_data = firebase_manager.get_data(f"finalized_contracts/{contract_id}")

        if not contract_data:
            return {'error': 'العقد غير موجود'}, 404

        # إرجاع جميع بيانات العقد مع الصور
        return {
            'success': True,
            'contract_data': contract_data.get('contract_data', {}),
            'images': contract_data.get('images', {}),
            'contract_id': contract_id,
            'finalized_at': contract_data.get('finalized_at', ''),
            'status': contract_data.get('status', '')
        }

    except Exception as e:
        return {'error': f'خطأ في جلب بيانات العقد: {str(e)}'}, 500

# صفحة تعديل العقد
@app.route('/edit_contract/<contract_id>')
def edit_contract_page(contract_id):
    if 'computer_id' not in session:
        return redirect(url_for('login'))

    try:
        from firebase_manager import firebase_manager

        # جلب بيانات العقد من Firebase
        contract_data = firebase_manager.get_data(f"finalized_contracts/{contract_id}")

        if not contract_data:
            return "العقد غير موجود", 404

        # تمرير بيانات العقد إلى الصفحة مع سجل التعديلات
        return render_template('edit_contract.html',
                             contract_id=contract_id,
                             contract_data=contract_data.get('contract_data', {}),
                             images=contract_data.get('images', {}),
                             modifications_history=contract_data.get('modifications_history', []),
                             computer_id=session.get('computer_id'),
                             computer_name=session.get('computer_name'))

    except Exception as e:
        return f"خطأ في تحميل صفحة التعديل: {str(e)}", 500

# API لتحديث العقد
@app.route('/api/update_contract/<contract_id>', methods=['POST'])
def update_contract(contract_id):
    if 'computer_id' not in session:
        return {'error': 'غير مصرح'}, 401

    try:
        from firebase_manager import firebase_manager

        computer_name = session.get('computer_name')

        # التحقق من وجود العقد
        existing_contract = firebase_manager.get_data(f"finalized_contracts/{contract_id}")
        if not existing_contract:
            return {'error': 'العقد غير موجود'}, 404

        # جمع البيانات من النموذج
        currency_type = request.form.get('currency_type', 'IQD')
        badal_num_raw = request.form.get('badal_num', '').replace(',', '')
        mony_num_raw = request.form.get('mony_num', '').replace(',', '')

        # حساب المبلغ المتبقي
        try:
            badal_amount = int(badal_num_raw) if badal_num_raw else 0
            mony_amount = int(mony_num_raw) if mony_num_raw else 0
            remaining_amount = badal_amount - mony_amount
        except ValueError:
            badal_amount = 0
            mony_amount = 0
            remaining_amount = 0

        # تنسيق المبالغ مع النص العربي
        badal_formatted = format_number_with_currency(badal_amount, currency_type)
        badal_text = number_to_arabic_text(badal_amount, currency_type)
        mony_formatted = format_number_with_currency(mony_amount, currency_type)
        mony_text = number_to_arabic_text(mony_amount, currency_type)
        remaining_formatted = format_number_with_currency(remaining_amount, currency_type)
        remaining_text = number_to_arabic_text(remaining_amount, currency_type)

        # إعداد البيانات المحدثة مع الحفاظ على UUID الأصلي
        updated_data = {
            'date': request.form.get('date', ''),
            'date_1': request.form.get('date', ''),
            't': request.form.get('t', ''),
            't_1': request.form.get('t_1', ''),
            't_11': request.form.get('t_1', ''),
            't_': request.form.get('t', ''),
            'day': request.form.get('day', ''),
            'serial_number': request.form.get('serial_number', ''),
            'name_1': request.form.get('name_1', ''),
            'location_1': request.form.get('location_1', ''),
            'name_2': request.form.get('name_2', ''),
            'name_22': request.form.get('name_2', ''),
            'location_2': request.form.get('location_2', ''),
            'id_1': request.form.get('id_1', ''),
            'phone_1': request.form.get('phone_1', ''),
            'name_3': request.form.get('name_3', ''),
            'name_33': request.form.get('name_3', ''),
            'location_3': request.form.get('location_3', ''),
            'id_2': request.form.get('id_2', ''),
            'phone_2': request.form.get('phone_2', ''),
            'sin_num': request.form.get('sin_num', ''),
            'car_num': request.form.get('car_num', ''),
            'car_prop': request.form.get('car_prop', ''),
            'car_type': request.form.get('car_type', ''),
            'car_model': request.form.get('car_model', ''),
            'car_colar': request.form.get('car_colar', ''),
            'sasi_num': request.form.get('sasi_num', ''),
            'badal_num': badal_formatted,
            'badal_writing': badal_text,
            'mony_num': mony_formatted,
            'mony_writing': mony_text,
            'mony_not_delevired': remaining_formatted,
            'mony_not_delevired_writing': remaining_text,
            'note_a': request.form.get('note_a', ''),
            'note_b': request.form.get('note_b', ''),
            'm1': request.form.get('m1', ''),
            'car_city': request.form.get('car_city', ''),
            'currency_type': currency_type,
            'uuid': existing_contract.get('uuid', ''),  # الحفاظ على UUID الأصلي
        }

        # تتبع التعديلات - مقارنة القيم القديمة مع الجديدة
        old_data = existing_contract.get('contract_data', {})
        modifications = []

        # قائمة الحقول التي نريد تتبع تعديلها مع أسمائها العربية
        fields_to_track = {
            'name_1': 'اسم المالك الشرعي',
            'location_1': 'عنوان المالك الشرعي',
            'name_2': 'اسم البائع',
            'location_2': 'عنوان البائع',
            'id_1': 'رقم هوية البائع',
            'phone_1': 'هاتف البائع',
            'name_3': 'اسم المشتري',
            'location_3': 'عنوان المشتري',
            'id_2': 'رقم هوية المشتري',
            'phone_2': 'هاتف المشتري',
            'car_type': 'نوع السيارة',
            'car_model': 'موديل السيارة',
            'car_num': 'رقم السيارة',
            'car_colar': 'لون السيارة',
            'sasi_num': 'رقم الشاصي',
            'car_prop': 'خصائص السيارة',
            'car_city': 'مدينة التسجيل',
            'sin_num': 'رقم السند',
            'badal_num': 'سعر السيارة',
            'mony_num': 'المبلغ الواصل',
            'note_a': 'ملاحظة أ',
            'note_b': 'ملاحظة ب',
            'm1': 'ملاحظات الباقي',
            'date': 'تاريخ العقد',
            't': 'الوقت',
            't_1': 'الفترة',
            'day': 'اليوم',
            'serial_number': 'الرقم التسلسلي'
        }

        # مقارنة القيم وتسجيل التعديلات
        for field, arabic_name in fields_to_track.items():
            old_value = old_data.get(field, '')
            new_value = updated_data.get(field, '')

            # تنظيف القيم للمقارنة (إزالة المسافات والفواصل)
            old_clean = str(old_value).strip() if old_value else ''
            new_clean = str(new_value).strip() if new_value else ''

            # للحقول المالية، نقارن القيم الرقمية
            if field in ['badal_num', 'mony_num']:
                try:
                    old_clean = str(int(float(str(old_value).replace(',', '').replace('$', '').split()[0]))) if old_value else '0'
                    new_clean = str(int(float(str(new_value).replace(',', '').replace('$', '').split()[0]))) if new_value else '0'
                except:
                    pass

            if old_clean != new_clean and new_clean:  # تسجيل فقط إذا كان هناك تغيير وقيمة جديدة
                modifications.append({
                    'field': arabic_name,
                    'old_value': old_clean if old_clean else 'فارغ',
                    'new_value': new_clean,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'modified_by': 'مكتب دبي'
                })

        # معالجة الصور
        updated_images = existing_contract.get('images', {})

        seller_photo = request.form.get('sellerPhoto', '')
        buyer_photo = request.form.get('buyerPhoto', '')

        if seller_photo:
            updated_images['sellerPhoto'] = seller_photo
        if buyer_photo:
            updated_images['buyerPhoto'] = buyer_photo

        # إضافة التعديلات الجديدة إلى سجل التعديلات الموجود
        existing_modifications = existing_contract.get('modifications_history', [])
        if modifications:  # إضافة فقط إذا كان هناك تعديلات
            existing_modifications.extend(modifications)

        # تحديث بيانات العقد في Firebase مع الحفاظ على UUID الأصلي
        updated_contract = {
            'contract_data': updated_data,
            'images': updated_images,
            'status': 'finalized',
            'finalized_at': existing_contract.get('finalized_at', ''),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'updated_by': computer_name,
            'word_filename': existing_contract.get('word_filename', ''),
            'pdf_filename': existing_contract.get('pdf_filename', ''),
            'uuid': existing_contract.get('uuid', ''),  # الحفاظ على UUID الأصلي
            'pdf_converted': existing_contract.get('pdf_converted', False),  # الحفاظ على حالة التحويل
            'modifications_history': existing_modifications  # سجل جميع التعديلات
        }

        # حفظ التحديثات في Firebase
        firebase_path = f"finalized_contracts/{contract_id}"
        success = firebase_manager.set_data(firebase_path, updated_contract)

        if success:
            # إنشاء ملف Word محدث (اختياري)
            try:
                # حفظ الصور مؤقتاً إذا كانت موجودة
                seller_photo_path = None
                buyer_photo_path = None

                if seller_photo:
                    seller_photo_path = os.path.join(UPLOAD_FOLDER, f'temp_seller_edit_{contract_id}.jpg')
                    with open(seller_photo_path, 'wb') as f:
                        f.write(base64.b64decode(seller_photo))

                if buyer_photo:
                    buyer_photo_path = os.path.join(UPLOAD_FOLDER, f'temp_buyer_edit_{contract_id}.jpg')
                    with open(buyer_photo_path, 'wb') as f:
                        f.write(base64.b64decode(buyer_photo))

                # إنشاء ملف Word محدث
                updated_word_filename = f'contract_updated_{contract_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx'
                updated_word_path = os.path.join(UPLOAD_FOLDER, updated_word_filename)

                fill_word_template('template.docx', updated_word_path, updated_data, seller_photo_path, buyer_photo_path)

                # تنظيف الملفات المؤقتة
                if seller_photo_path and os.path.exists(seller_photo_path):
                    os.remove(seller_photo_path)
                if buyer_photo_path and os.path.exists(buyer_photo_path):
                    os.remove(buyer_photo_path)

                # تحديث اسم الملف في Firebase
                updated_contract['word_filename'] = updated_word_filename
                firebase_manager.set_data(firebase_path, updated_contract)

            except Exception as e:
                print(f"خطأ في إنشاء ملف Word محدث: {e}")

            return {'success': True, 'message': 'تم تحديث العقد بنجاح', 'contract_id': contract_id}
        else:
            return {'error': 'فشل في حفظ التحديثات في قاعدة البيانات'}, 500

    except Exception as e:
        return {'error': f'خطأ في تحديث العقد: {str(e)}'}, 500

# API لتحويل الأرقام إلى النص العربي
@app.route('/api/convert_to_arabic', methods=['POST'])
def convert_to_arabic_api():
    try:
        data = request.json
        number = data.get('number', 0)
        currency_type = data.get('currency_type', 'IQD')

        arabic_text = number_to_arabic_text(number, currency_type)

        return {
            'success': True,
            'arabic_text': arabic_text,
            'formatted_number': format_number_with_currency(number, currency_type)
        }
    except Exception as e:
        return {'error': f'خطأ في تحويل الرقم: {str(e)}'}, 500

# ===== مسارات تحميل عقود السنة =====

# API لجلب السنوات المتاحة للعقود المبرمة
@app.route('/admin/api/available_years')
def get_available_years_api():
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    try:
        years = get_available_years()
        return {'success': True, 'years': years}
    except Exception as e:
        return {'error': f'خطأ في جلب السنوات: {str(e)}'}, 500

# API لجلب عقود سنة معينة
@app.route('/admin/api/contracts_by_year/<int:year>')
def get_contracts_by_year_api(year):
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    try:
        contracts = get_finalized_contracts_by_year(year)

        # تحويل البيانات لتنسيق مناسب للعرض
        contracts_list = []
        for contract_id, contract_data in contracts.items():
            contract_info = {
                'contract_id': contract_id,
                'serial_number': contract_data.get('contract_data', {}).get('serial_number', ''),
                'seller_name': contract_data.get('contract_data', {}).get('name_2', ''),
                'buyer_name': contract_data.get('contract_data', {}).get('name_3', ''),
                'finalized_at': contract_data.get('finalized_at', ''),
                'sale_amount': contract_data.get('contract_data', {}).get('badal_num', ''),
                'car_type': contract_data.get('contract_data', {}).get('car_type', ''),
                'car_model': contract_data.get('contract_data', {}).get('car_model', ''),
            }
            contracts_list.append(contract_info)

        # ترتيب حسب الرقم التسلسلي
        contracts_list.sort(key=lambda x: int(x.get('serial_number', 0) or 0))

        return {
            'success': True,
            'year': year,
            'contracts': contracts_list,
            'total_count': len(contracts_list)
        }
    except Exception as e:
        return {'error': f'خطأ في جلب عقود السنة: {str(e)}'}, 500

# متغيرات عامة لتتبع تقدم التصدير
export_progress = {}
export_progress_lock = threading.Lock()

def cleanup_hanging_processes():
    """تنظيف العمليات المعلقة لـ Word و LibreOffice"""
    try:
        if platform.system() == "Windows":
            # إنهاء عمليات Word
            subprocess.run(['taskkill', '/f', '/im', 'WINWORD.EXE'],
                         capture_output=True, timeout=5)
            # إنهاء عمليات LibreOffice
            subprocess.run(['taskkill', '/f', '/im', 'soffice.exe'],
                         capture_output=True, timeout=5)
            subprocess.run(['taskkill', '/f', '/im', 'soffice.bin'],
                         capture_output=True, timeout=5)
        else:
            # إنهاء عمليات LibreOffice في Linux/Mac
            subprocess.run(['pkill', '-f', 'soffice'], capture_output=True, timeout=5)
            subprocess.run(['pkill', '-f', 'libreoffice'], capture_output=True, timeout=5)

        time.sleep(2)  # انتظار لإنهاء العمليات
        print("🧹 تم تنظيف العمليات المعلقة")
    except Exception as e:
        print(f"⚠️ خطأ في تنظيف العمليات المعلقة: {e}")


# تم حذف دوال LibreOffice - يتم استخدام Gradio API حصرياً


# تم حذف دوال تثبيت الحزم القديمة - نحتاج فقط requests لـ Gradio API


def monitor_memory_usage():
    """مراقبة استخدام الذاكرة وتحسين الأداء مع النظام الجديد"""
    try:
        memory_mb, cpu_percent = get_system_stats()

        # تحسين حدود الذاكرة للأداء الأفضل
        if memory_mb > 400:  # تقليل الحد الأقصى لتحسين الأداء
            print(f"⚠️ استخدام ذاكرة عالي: {memory_mb:.1f} MB, CPU: {cpu_percent:.1f}%")
            # استخدام النظام الجديد للتنظيف
            perform_memory_cleanup()

        return memory_mb
    except Exception as e:
        print(f"⚠️ خطأ في مراقبة الذاكرة: {e}")
        return 0


# تم حذف دوال تحسين PDF القديمة - نستخدم Gradio API حصرياً


def save_error_report(year, failed_contracts, total_contracts):
    """حفظ تقرير الأخطاء في ملف"""
    try:
        error_report = {
            'year': year,
            'timestamp': datetime.now().isoformat(),
            'total_contracts': total_contracts,
            'failed_contracts': failed_contracts,
            'success_rate': ((total_contracts - len(failed_contracts)) / total_contracts * 100) if total_contracts > 0 else 0,
            'system_info': {
                'platform': platform.system(),
                'gradio_api_url': GRADIO_API_URL,
                'gradio_available': check_gradio_api_status(),
                'memory_usage_mb': monitor_memory_usage()
            }
        }

        error_report_path = os.path.join(UPLOAD_FOLDER, f'error_report_{year}_{int(time.time())}.json')
        with open(error_report_path, 'w', encoding='utf-8') as f:
            json.dump(error_report, f, ensure_ascii=False, indent=2)

        print(f"📄 تم حفظ تقرير الأخطاء: {error_report_path}")
        return error_report_path

    except Exception as e:
        print(f"❌ فشل في حفظ تقرير الأخطاء: {e}")
        return None


# دالة معالجة عقد واحد - إنشاء Word فقط
def create_word_contract(contract_id, contract_data, contract_index, temp_dir):
    """
    إنشاء ملف Word للعقد فقط (المرحلة الأولى)
    """
    try:
        print(f"📄 إنشاء ملف Word للعقد {contract_index}")

        # استخراج البيانات
        contract_info = contract_data.get('contract_data', {})
        images_data = contract_data.get('images', {})

        # تحضير البيانات للعقد
        data = {
            'date': contract_info.get('date', ''),
            'date_1': contract_info.get('date_1', ''),
            't': contract_info.get('t', ''),
            't_1': contract_info.get('t_1', ''),
            't_11': contract_info.get('t_11', ''),
            't_': contract_info.get('t_', ''),
            'day': contract_info.get('day', ''),
            'serial_number': contract_info.get('serial_number', ''),
            'name_1': contract_info.get('name_1', ''),
            'location_1': contract_info.get('location_1', ''),
            'name_2': contract_info.get('name_2', ''),
            'name_22': contract_info.get('name_2', ''),
            'location_2': contract_info.get('location_2', ''),
            'id_1': contract_info.get('id_1', ''),
            'phone_1': contract_info.get('phone_1', ''),
            'name_3': contract_info.get('name_3', ''),
            'name_33': contract_info.get('name_3', ''),
            'location_3': contract_info.get('location_3', ''),
            'id_2': contract_info.get('id_2', ''),
            'phone_2': contract_info.get('phone_2', ''),
            'sin_num': contract_info.get('sin_num', ''),
            'car_num': contract_info.get('car_num', ''),
            'car_prop': contract_info.get('car_prop', ''),
            'car_type': contract_info.get('car_type', ''),
            'car_model': contract_info.get('car_model', ''),
            'car_colar': contract_info.get('car_colar', ''),
            'sasi_num': contract_info.get('sasi_num', ''),
            'badal_num': contract_info.get('badal_num', ''),
            'badal_writing': contract_info.get('badal_writing', ''),
            'mony_num': contract_info.get('mony_num', ''),
            'mony_writing': contract_info.get('mony_writing', ''),
            'mony_not_delevired': contract_info.get('mony_not_delevired', ''),
            'mony_not_delevired_writing': contract_info.get('mony_not_delevired_writing', ''),
            'note_a': contract_info.get('note_a', ''),
            'note_b': contract_info.get('note_b', ''),
            'm1': contract_info.get('m1', ''),
            'car_city': contract_info.get('car_city', ''),
            'currency_type': contract_info.get('currency_type', 'IQD'),
            'qr': contract_info.get('qr', '')
        }

        # معالجة الصور
        seller_photo_path = None
        buyer_photo_path = None

        # معالجة صورة البائع
        if images_data.get('sellerPhoto'):
            try:
                seller_photo_data = images_data['sellerPhoto']
                if seller_photo_data.startswith('data:image'):
                    seller_photo_data = seller_photo_data.split(',')[1]

                seller_photo_path = os.path.join(temp_dir, f'seller_{contract_index}.png')
                with open(seller_photo_path, 'wb') as f:
                    f.write(base64.b64decode(seller_photo_data))
                print(f"📷 تم حفظ صورة البائع للعقد {contract_index}")
            except Exception as e:
                print(f"⚠️ خطأ في معالجة صورة البائع للعقد {contract_index}: {e}")

        # معالجة صورة المشتري
        if images_data.get('buyerPhoto'):
            try:
                buyer_photo_data = images_data['buyerPhoto']
                if buyer_photo_data.startswith('data:image'):
                    buyer_photo_data = buyer_photo_data.split(',')[1]

                buyer_photo_path = os.path.join(temp_dir, f'buyer_{contract_index}.png')
                with open(buyer_photo_path, 'wb') as f:
                    f.write(base64.b64decode(buyer_photo_data))
                print(f"📷 تم حفظ صورة المشتري للعقد {contract_index}")
            except Exception as e:
                print(f"⚠️ خطأ في معالجة صورة المشتري للعقد {contract_index}: {e}")

        # معالجة QR code
        contract_uuid = contract_data.get('uuid') or contract_info.get('uuid')
        if not contract_uuid:
            saved_contract_data = contract_data.get('form_data', {})
            contract_uuid = saved_contract_data.get('uuid')

        if not contract_uuid:
            contract_uuid = str(uuid.uuid4())
            data['uuid'] = contract_uuid
            print(f"⚠️ تم إنشاء UUID جديد للعقد {contract_index}")

        # توليد QR code للعقد
        qr_code_path = None
        qr_code_generated_new = False
        if images_data.get('qrCode'):
            try:
                qr_code_data = images_data['qrCode']
                qr_code_path = os.path.join(temp_dir, f'qr_{contract_index}.png')
                with open(qr_code_path, 'wb') as f:
                    f.write(base64.b64decode(qr_code_data))
                print(f"✅ تم استخدام QR Code المحفوظ للعقد {contract_index}")
            except Exception as e:
                print(f"❌ خطأ في استخدام QR Code المحفوظ للعقد {contract_index}: {e}")
                qr_code_path = generate_qr_code(contract_uuid, data.get('serial_number', contract_index))
                if qr_code_path:
                    qr_code_generated_new = True
                    print(f"🔄 تم توليد QR Code جديد بعد فشل استخدام المحفوظ للعقد {contract_index}")
        else:
            qr_code_path = generate_qr_code(contract_uuid, data.get('serial_number', contract_index))
            if qr_code_path:
                qr_code_generated_new = True
                print(f"🔗 تم توليد QR Code جديد للعقد {contract_index}")

        # حفظ QR Code الجديد في Firebase إذا تم توليده
        if qr_code_generated_new and qr_code_path:
            save_qr_code_to_firebase(contract_id, qr_code_path)

        # إنشاء ملف Word للعقد
        contract_filename = f'export_contract_{contract_index}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx'
        contract_path = os.path.join(temp_dir, contract_filename)
        print(f"📄 إنشاء ملف Word للعقد {contract_index}: {contract_filename}")

        # تعبئة الملف بالبيانات مع QR code
        fill_word_template('template.docx', contract_path, data, seller_photo_path, buyer_photo_path, qr_code_path)
        print(f"✅ تم إنشاء ملف Word بنجاح للعقد {contract_index}")

        # تنظيف الملفات المؤقتة (الصور و QR code فقط، نحتفظ بملف Word)
        cleanup_temp_files = [seller_photo_path, buyer_photo_path, qr_code_path]
        for temp_file in cleanup_temp_files:
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"🗑️ تم حذف الملف المؤقت: {os.path.basename(temp_file)}")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف الملف المؤقت {temp_file}: {e}")

        return {
            'success': True,
            'word_path': contract_path,
            'contract_index': contract_index,
            'contract_id': contract_id
        }

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف Word للعقد {contract_index}: {e}")
        return {
            'success': False,
            'error': str(e),
            'contract_index': contract_index,
            'contract_id': contract_id
        }

# دالة تحويل ملف Word إلى PDF
def convert_word_to_pdf(word_path, contract_index):
    """
    تحويل ملف Word إلى PDF (المرحلة الثانية)
    """
    try:
        print(f"🔄 تحويل Word إلى PDF للعقد {contract_index}")

        # تحديد مسار ملف PDF
        pdf_path = word_path.replace('.docx', '.pdf')

        # تحويل Word إلى PDF عبر Gradio API
        pdf_converted = convert_word_to_pdf_via_gradio(word_path, pdf_path)

        if pdf_converted and os.path.exists(pdf_path):
            print(f"✅ نجح تحويل PDF للعقد {contract_index}")

            # حذف ملف Word بعد التحويل الناجح
            try:
                os.remove(word_path)
                print(f"🗑️ تم حذف ملف Word بعد التحويل: {os.path.basename(word_path)}")
            except Exception as e:
                print(f"⚠️ خطأ في حذف ملف Word: {e}")

            return {
                'success': True,
                'pdf_path': pdf_path,
                'contract_index': contract_index
            }
        else:
            return {
                'success': False,
                'error': 'فشل في تحويل Word إلى PDF عبر Gradio API',
                'contract_index': contract_index
            }

    except Exception as e:
        print(f"❌ خطأ في تحويل Word إلى PDF للعقد {contract_index}: {e}")
        return {
            'success': False,
            'error': str(e),
            'contract_index': contract_index
        }

# دالة معالجة عقد واحد باستخدام نفس منطق إبرام العقد الفردي (للتوافق مع الكود القديم)
def process_single_contract_unified(contract_id, contract_data, contract_index, temp_dir):
    """
    معالجة عقد واحد باستخدام نفس منطق إبرام العقد الفردي
    """
    try:
        print(f"🔄 معالجة العقد {contract_index} باستخدام النظام الموحد")

        # استخراج البيانات
        contract_info = contract_data.get('contract_data', {})
        images_data = contract_data.get('images', {})

        # تحضير البيانات للعقد - نفس الطريقة المستخدمة في إبرام العقد الفردي
        data = {
            'date': contract_info.get('date', ''),
            'date_1': contract_info.get('date_1', ''),
            't': contract_info.get('t', ''),
            't_1': contract_info.get('t_1', ''),
            't_11': contract_info.get('t_11', ''),
            't_': contract_info.get('t_', ''),
            'day': contract_info.get('day', ''),
            'serial_number': contract_info.get('serial_number', ''),
            'name_1': contract_info.get('name_1', ''),
            'location_1': contract_info.get('location_1', ''),
            'name_2': contract_info.get('name_2', ''),
            'name_22': contract_info.get('name_2', ''),  # نسخة إضافية للبائع
            'location_2': contract_info.get('location_2', ''),
            'id_1': contract_info.get('id_1', ''),
            'phone_1': contract_info.get('phone_1', ''),
            'name_3': contract_info.get('name_3', ''),
            'name_33': contract_info.get('name_3', ''),  # نسخة إضافية للمشتري
            'location_3': contract_info.get('location_3', ''),
            'id_2': contract_info.get('id_2', ''),
            'phone_2': contract_info.get('phone_2', ''),
            'sin_num': contract_info.get('sin_num', ''),
            'car_num': contract_info.get('car_num', ''),
            'car_prop': contract_info.get('car_prop', ''),
            'car_type': contract_info.get('car_type', ''),
            'car_model': contract_info.get('car_model', ''),
            'car_colar': contract_info.get('car_colar', ''),
            'sasi_num': contract_info.get('sasi_num', ''),
            'badal_num': contract_info.get('badal_num', ''),
            'badal_writing': contract_info.get('badal_writing', ''),
            'mony_num': contract_info.get('mony_num', ''),
            'mony_writing': contract_info.get('mony_writing', ''),
            'mony_not_delevired': contract_info.get('mony_not_delevired', ''),
            'mony_not_delevired_writing': contract_info.get('mony_not_delevired_writing', ''),
            'note_a': contract_info.get('note_a', ''),
            'note_b': contract_info.get('note_b', ''),
            'm1': contract_info.get('m1', ''),
            'car_city': contract_info.get('car_city', ''),
            'currency_type': contract_info.get('currency_type', 'IQD'),
            'qr': contract_info.get('qr', '')
        }

        # معالجة الصور - نفس الطريقة المستخدمة في إبرام العقد الفردي
        seller_photo_path = None
        buyer_photo_path = None

        # معالجة صورة البائع
        if images_data.get('sellerPhoto'):
            try:
                seller_photo_data = images_data['sellerPhoto']
                if seller_photo_data.startswith('data:image'):
                    seller_photo_data = seller_photo_data.split(',')[1]

                seller_photo_path = os.path.join(temp_dir, f'seller_{contract_index}.png')
                with open(seller_photo_path, 'wb') as f:
                    f.write(base64.b64decode(seller_photo_data))
                print(f"📷 تم حفظ صورة البائع للعقد {contract_index}")
            except Exception as e:
                print(f"⚠️ خطأ في معالجة صورة البائع للعقد {contract_index}: {e}")

        # معالجة صورة المشتري
        if images_data.get('buyerPhoto'):
            try:
                buyer_photo_data = images_data['buyerPhoto']
                if buyer_photo_data.startswith('data:image'):
                    buyer_photo_data = buyer_photo_data.split(',')[1]

                buyer_photo_path = os.path.join(temp_dir, f'buyer_{contract_index}.png')
                with open(buyer_photo_path, 'wb') as f:
                    f.write(base64.b64decode(buyer_photo_data))
                print(f"📷 تم حفظ صورة المشتري للعقد {contract_index}")
            except Exception as e:
                print(f"⚠️ خطأ في معالجة صورة المشتري للعقد {contract_index}: {e}")

        # معالجة QR code - نفس الطريقة المستخدمة في إبرام العقد الفردي
        contract_uuid = contract_data.get('uuid') or contract_info.get('uuid')
        if not contract_uuid:
            saved_contract_data = contract_data.get('form_data', {})
            contract_uuid = saved_contract_data.get('uuid')

        if not contract_uuid:
            # إنشاء UUID جديد إذا لم يكن موجود - نفس الطريقة المستخدمة في إبرام العقد الفردي
            contract_uuid = str(uuid.uuid4())
            data['uuid'] = contract_uuid
            print(f"⚠️ تم إنشاء UUID جديد للعقد {contract_index}")

        # توليد QR code للعقد - نفس الطريقة المستخدمة في إبرام العقد الفردي
        qr_code_path = None
        qr_code_generated_new = False
        if images_data.get('qrCode'):
            try:
                qr_code_data = images_data['qrCode']
                qr_code_path = os.path.join(temp_dir, f'qr_{contract_index}.png')
                with open(qr_code_path, 'wb') as f:
                    f.write(base64.b64decode(qr_code_data))
                print(f"✅ تم استخدام QR Code المحفوظ للعقد {contract_index}")
            except Exception as e:
                print(f"❌ خطأ في استخدام QR Code المحفوظ للعقد {contract_index}: {e}")
                # في حالة الفشل، توليد QR code جديد
                qr_code_path = generate_qr_code(contract_uuid, data.get('serial_number', contract_index))
                if qr_code_path:
                    qr_code_generated_new = True
                    print(f"🔄 تم توليد QR Code جديد بعد فشل استخدام المحفوظ للعقد {contract_index}")
        else:
            # إذا لم يكن QR Code محفوظاً، توليد جديد
            qr_code_path = generate_qr_code(contract_uuid, data.get('serial_number', contract_index))
            if qr_code_path:
                qr_code_generated_new = True
                print(f"🔗 تم توليد QR Code جديد للعقد {contract_index}")

        # حفظ QR Code الجديد في Firebase إذا تم توليده
        if qr_code_generated_new and qr_code_path:
            save_qr_code_to_firebase(contract_id, qr_code_path)

        # إنشاء ملف Word للعقد - نفس الطريقة المستخدمة في إبرام العقد الفردي
        contract_filename = f'export_contract_{contract_index}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx'
        contract_path = os.path.join(temp_dir, contract_filename)
        print(f"📄 إنشاء ملف Word للعقد {contract_index}: {contract_filename}")

        # تعبئة الملف بالبيانات مع QR code - نفس الطريقة المستخدمة في إبرام العقد الفردي
        fill_word_template('template.docx', contract_path, data, seller_photo_path, buyer_photo_path, qr_code_path)
        print(f"✅ تم إنشاء ملف Word بنجاح للعقد {contract_index}")

        # تحويل Word إلى PDF باستخدام نفس نظام الطابور المستخدم في إبرام العقد الفردي
        pdf_filename = contract_filename.replace('.docx', '.pdf')
        pdf_path = os.path.join(temp_dir, pdf_filename)
        contract_export_id = f"export_contract_{contract_index}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        print(f"🔄 تحويل Word إلى PDF للعقد {contract_index} باستخدام Gradio API")

        # استخدام Gradio API لمعالجة PDF
        pdf_result = process_pdf_with_gradio(contract_path, pdf_path, contract_export_id)

        if pdf_result and pdf_result.get('success') and os.path.exists(pdf_path):
            print(f"✅ نجح تحويل PDF للعقد {contract_index}")

            # تنظيف الملفات المؤقتة - نفس الطريقة المستخدمة في إبرام العقد الفردي
            cleanup_temp_files = [seller_photo_path, buyer_photo_path, qr_code_path, contract_path]
            for temp_file in cleanup_temp_files:
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                        print(f"🗑️ تم حذف الملف المؤقت: {os.path.basename(temp_file)}")
                    except Exception as e:
                        print(f"⚠️ خطأ في حذف الملف المؤقت {temp_file}: {e}")

            return {
                'success': True,
                'pdf_path': pdf_path,
                'contract_index': contract_index,
                'contract_id': contract_export_id
            }
        else:
            error_msg = pdf_result.get('error', 'خطأ غير معروف في تحويل PDF') if pdf_result else 'فشل في معالجة PDF'
            print(f"❌ فشل تحويل PDF للعقد {contract_index}: {error_msg}")

            # تنظيف الملفات المؤقتة حتى في حالة الفشل
            cleanup_temp_files = [seller_photo_path, buyer_photo_path, qr_code_path, contract_path]
            for temp_file in cleanup_temp_files:
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass

            return {
                'success': False,
                'contract_index': contract_index,
                'error': error_msg
            }

    except Exception as e:
        print(f"❌ خطأ في معالجة العقد {contract_index}: {e}")

        # تنظيف الملفات المؤقتة في حالة الخطأ
        try:
            cleanup_temp_files = [seller_photo_path, buyer_photo_path, qr_code_path]
            for temp_file in cleanup_temp_files:
                if temp_file and os.path.exists(temp_file):
                    os.remove(temp_file)
        except:
            pass

        return {
            'success': False,
            'contract_index': contract_index,
            'error': str(e)
        }

def process_contract_batch(batch_data):
    """معالجة مجموعة من العقود بشكل متوازي مع معالجة محسنة للأخطاء والأداء"""
    batch_id, contracts_batch, temp_dir, start_index = batch_data
    pdf_files = []
    failed_contracts = []

    # تنظيف العمليات المعلقة قبل البدء
    cleanup_hanging_processes()

    try:
        # معالجة العقود مع تحسينات الأداء
        for i, (contract_id, contract_data) in enumerate(contracts_batch):
            actual_index = start_index + i

            # استخدام النظام الموحد الجديد لمعالجة العقد
            result = process_single_contract_unified(contract_id, contract_data, actual_index, temp_dir)

            if result['success']:
                pdf_files.append(result['pdf_path'])
                print(f"✅ المجموعة {batch_id}: تم إضافة العقد {actual_index} إلى قائمة PDF")
            else:
                failed_contracts.append(actual_index)
                print(f"❌ المجموعة {batch_id}: فشل في معالجة العقد {actual_index}: {result['error']}")

            # تنظيف العمليات المعلقة كل 5 عقود لتحسين الأداء وتقليل استهلاك CPU
            if (i + 1) % 5 == 0:
                schedule_cleanup_task('hanging_processes')
                schedule_cleanup_task('memory_cleanup')
                # فترة راحة قصيرة لتقليل الضغط على النظام
                time.sleep(2)

        # تقرير عن النتائج
        if failed_contracts:
            print(f"⚠️ المجموعة {batch_id}: فشل في تحويل {len(failed_contracts)} عقد: {failed_contracts}")

        print(f"📊 المجموعة {batch_id}: نجح {len(pdf_files)} من أصل {len(contracts_batch)} عقد")

        # مراقبة استخدام الذاكرة
        memory_usage = monitor_memory_usage()
        if memory_usage > 250:  # تقليل الحد الأقصى لتحسين الأداء
            print(f"⚠️ المجموعة {batch_id}: استخدام ذاكرة عالي {memory_usage:.1f} MB")
            # تنظيف إضافي للذاكرة
            import gc
            gc.collect()

        return {'pdf_files': pdf_files, 'failed_contracts': failed_contracts}
    except Exception as e:
        print(f"❌ خطأ في معالجة المجموعة {batch_id}: {e}")
        # تنظيف العمليات المعلقة في حالة الخطأ
        cleanup_hanging_processes()
        return {'pdf_files': [], 'failed_contracts': list(range(start_index, start_index + len(contracts_batch)))}

# دالة معالجة العقود على مرحلتين - إنشاء Word أولاً ثم تحويل إلى PDF
def process_contracts_two_phase(contracts_list, temp_dir, max_workers=4):
    """
    معالجة العقود على مرحلتين:
    المرحلة الأولى: إنشاء جميع ملفات Word
    المرحلة الثانية: تحويل جميع ملفات Word إلى PDF عبر Gradio API
    """
    try:
        from concurrent.futures import ThreadPoolExecutor, as_completed

        total_contracts = len(contracts_list)
        print(f"🚀 بدء معالجة {total_contracts} عقد على مرحلتين")

        # المرحلة الأولى: إنشاء جميع ملفات Word
        print("📄 المرحلة الأولى: إنشاء ملفات Word...")
        word_files = []
        failed_word_creation = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # إرسال جميع العقود لإنشاء ملفات Word
            future_to_contract = {
                executor.submit(create_word_contract, contract_id, contract_data, i, temp_dir): i
                for i, (contract_id, contract_data) in enumerate(contracts_list)
            }

            for future in as_completed(future_to_contract):
                contract_index = future_to_contract[future]
                try:
                    result = future.result()
                    if result['success']:
                        word_files.append({
                            'word_path': result['word_path'],
                            'contract_index': result['contract_index'],
                            'contract_id': result['contract_id']
                        })
                        print(f"✅ تم إنشاء Word للعقد {contract_index}")
                    else:
                        failed_word_creation.append(contract_index)
                        print(f"❌ فشل في إنشاء Word للعقد {contract_index}: {result.get('error', 'خطأ غير معروف')}")
                except Exception as e:
                    failed_word_creation.append(contract_index)
                    print(f"❌ خطأ في إنشاء Word للعقد {contract_index}: {e}")

        print(f"📊 تم إنشاء {len(word_files)} ملف Word من أصل {total_contracts}")

        if not word_files:
            return {
                'success': False,
                'error': 'فشل في إنشاء أي ملف Word',
                'pdf_files': [],
                'failed_contracts': list(range(total_contracts))
            }

        # المرحلة الثانية: تحويل جميع ملفات Word إلى PDF عبر Gradio API
        print("🔄 المرحلة الثانية: تحويل ملفات Word إلى PDF عبر Gradio API...")
        pdf_files = []
        failed_pdf_conversion = []

        # تحويل ملفات Word إلى PDF واحد تلو الآخر لتجنب إرهاق Gradio API
        for word_file_info in word_files:
            try:
                result = convert_word_to_pdf(word_file_info['word_path'], word_file_info['contract_index'])
                if result['success']:
                    pdf_files.append(result['pdf_path'])
                    print(f"✅ تم تحويل PDF للعقد {word_file_info['contract_index']}")
                else:
                    failed_pdf_conversion.append(word_file_info['contract_index'])
                    print(f"❌ فشل في تحويل PDF للعقد {word_file_info['contract_index']}: {result.get('error', 'خطأ غير معروف')}")

                # تأخير قصير بين كل تحويل لتجنب إرهاق Gradio API
                import time
                time.sleep(0.5)

            except Exception as e:
                failed_pdf_conversion.append(word_file_info['contract_index'])
                print(f"❌ خطأ في تحويل PDF للعقد {word_file_info['contract_index']}: {e}")

        print(f"📊 تم تحويل {len(pdf_files)} ملف PDF من أصل {len(word_files)} ملف Word")

        # دمج قوائم الأخطاء
        all_failed_contracts = failed_word_creation + failed_pdf_conversion

        return {
            'success': len(pdf_files) > 0,
            'pdf_files': pdf_files,
            'failed_contracts': all_failed_contracts,
            'word_created': len(word_files),
            'pdf_converted': len(pdf_files),
            'total_contracts': total_contracts
        }

    except Exception as e:
        print(f"❌ خطأ في معالجة العقود على مرحلتين: {e}")
        return {
            'success': False,
            'error': str(e),
            'pdf_files': [],
            'failed_contracts': list(range(len(contracts_list)))
        }

# API لتحميل عقود السنة كملف PDF واحد (محسن مع النظام الجديد على مرحلتين)
@app.route('/admin/api/export_year_contracts/<int:year>')
def export_year_contracts(year):
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    try:
        import tempfile
        import shutil
        from PyPDF2 import PdfMerger

        # جلب عقود السنة
        contracts = get_finalized_contracts_by_year(year)

        if not contracts:
            return {'error': f'لا توجد عقود لسنة {year}'}, 404

        # ترتيب العقود حسب الرقم التسلسلي
        contracts_list = list(contracts.items())
        contracts_list.sort(key=lambda x: int(x[1].get('contract_data', {}).get('serial_number', 0) or 0))

        total_contracts = len(contracts_list)
        print(f"🚀 بدء تصدير {total_contracts} عقد لسنة {year} باستخدام النظام الجديد على مرحلتين")

        # إنشاء مجلد مؤقت
        temp_dir = tempfile.mkdtemp()

        # تحديد عدد العمال بناءً على عدد العقود
        if total_contracts <= 50:
            max_workers = 2
        elif total_contracts <= 200:
            max_workers = 3
        elif total_contracts <= 500:
            max_workers = 4
        elif total_contracts <= 1000:
            max_workers = 5
        else:
            max_workers = 6

        print(f"📊 استخدام {max_workers} عمال للمعالجة على مرحلتين")

        try:
            # استخدام النظام الجديد على مرحلتين
            result = process_contracts_two_phase(contracts_list, temp_dir, max_workers)

            if not result['success']:
                return {'error': result.get('error', 'فشل في معالجة العقود')}, 500

            pdf_files = result['pdf_files']
            failed_contracts = result['failed_contracts']

            print(f"📊 النتائج النهائية:")
            print(f"   - تم إنشاء {result.get('word_created', 0)} ملف Word")
            print(f"   - تم تحويل {result.get('pdf_converted', 0)} ملف PDF")
            print(f"   - فشل في {len(failed_contracts)} عقد")

            # دمج جميع ملفات PDF في ملف واحد
            if pdf_files:
                print("🔗 بدء دمج ملفات PDF...")
                merger = PdfMerger()

                # ترتيب ملفات PDF حسب الفهرس
                pdf_files.sort(key=lambda x: int(os.path.basename(x).split('_')[2].split('.')[0]))

                for pdf_file in pdf_files:
                    try:
                        merger.append(pdf_file)
                        print(f"✅ تم إضافة ملف PDF: {os.path.basename(pdf_file)}")
                    except Exception as e:
                        print(f"❌ خطأ في إضافة ملف PDF {pdf_file}: {e}")

                # حفظ الملف المدموج
                final_pdf_path = os.path.join(temp_dir, f'contracts_export_{year}.pdf')
                with open(final_pdf_path, 'wb') as output_file:
                    merger.write(output_file)
                merger.close()

                print(f"✅ تم دمج جميع العقود في ملف واحد: {final_pdf_path}")

                # حفظ تقرير الأخطاء إذا كان هناك عقود فاشلة
                if failed_contracts:
                    save_error_report(year, failed_contracts, total_contracts)
                    print(f"⚠️ تم إنشاء تقرير الأخطاء: {len(failed_contracts)} عقد فشل من أصل {total_contracts}")

                # إرسال الملف مع الحذف التلقائي
                return send_file_with_auto_cleanup(
                    final_pdf_path,
                    f'contracts_export_{year}.pdf',
                    'application/pdf'
                )
            else:
                return {'error': 'فشل في إنشاء ملفات PDF للعقود'}, 500

        finally:
            # تنظيف الملفات المؤقتة
            try:
                shutil.rmtree(temp_dir)
                print("🧹 تم تنظيف الملفات المؤقتة")
            except Exception as e:
                print(f"خطأ في تنظيف الملفات المؤقتة: {e}")

    except ImportError as e:
        missing_lib = str(e).split("'")[1] if "'" in str(e) else "مكتبة مطلوبة"
        return {'error': f'مكتبة {missing_lib} غير مثبتة. يرجى تثبيتها لتصدير PDF'}, 500
    except Exception as e:
        return {'error': f'خطأ في تصدير العقود: {str(e)}'}, 500

# API لتتبع تقدم التصدير
@app.route('/admin/api/export_progress/<int:year>')
def get_export_progress(year):
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    with export_progress_lock:
        progress_data = export_progress.get(year, {
            'status': 'not_started',
            'progress': 0,
            'current_batch': 0,
            'total_batches': 0,
            'processed_contracts': 0,
            'total_contracts': 0,
            'estimated_time_remaining': 0,
            'start_time': None
        })

    return {'success': True, 'progress': progress_data}

# API محسن لتحميل عقود السنة مع تتبع التقدم
@app.route('/admin/api/export_year_contracts_async/<int:year>')
def export_year_contracts_async(year):
    # تحديث نشاط الطلبات
    update_request_activity()

    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    def export_worker():
        try:
            import tempfile
            import shutil
            from PyPDF2 import PdfMerger
            from concurrent.futures import ThreadPoolExecutor, as_completed
            import time

            # تحديث حالة التقدم
            with export_progress_lock:
                export_progress[year] = {
                    'status': 'initializing',
                    'progress': 0,
                    'current_batch': 0,
                    'total_batches': 0,
                    'processed_contracts': 0,
                    'total_contracts': 0,
                    'estimated_time_remaining': 0,
                    'start_time': time.time()
                }

            # جلب عقود السنة
            contracts = get_finalized_contracts_by_year(year)

            if not contracts:
                with export_progress_lock:
                    export_progress[year]['status'] = 'error'
                    export_progress[year]['error'] = f'لا توجد عقود لسنة {year}'
                return

            # ترتيب العقود حسب الرقم التسلسلي
            contracts_list = list(contracts.items())
            contracts_list.sort(key=lambda x: int(x[1].get('contract_data', {}).get('serial_number', 0) or 0))

            total_contracts = len(contracts_list)

            # تحديد عدد العمال بناءً على عدد العقود
            if total_contracts <= 50:
                max_workers = 2
            elif total_contracts <= 200:
                max_workers = 3
            elif total_contracts <= 500:
                max_workers = 4
            elif total_contracts <= 1000:
                max_workers = 5
            else:
                max_workers = 6

            # تحديث معلومات التقدم
            with export_progress_lock:
                export_progress[year].update({
                    'status': 'processing',
                    'total_contracts': total_contracts,
                    'total_batches': 2,  # مرحلتان: Word ثم PDF
                    'current_phase': 'word_creation'
                })

            # تنظيف العمليات المعلقة قبل البدء باستخدام النظام المركزي
            schedule_cleanup_task('hanging_processes')
            time.sleep(1)  # انتظار قصير للتأكد من التنظيف

            # إنشاء مجلد مؤقت
            temp_dir = tempfile.mkdtemp()
            print(f"📁 تم إنشاء مجلد مؤقت: {temp_dir}")

            try:
                # استخدام النظام الجديد على مرحلتين مع تتبع التقدم
                print(f"🚀 بدء معالجة {total_contracts} عقد باستخدام النظام الجديد على مرحلتين")

                # تحديث التقدم - بدء المرحلة الأولى
                with export_progress_lock:
                    export_progress[year].update({
                        'status': 'word_creation',
                        'progress': 10,
                        'current_phase': 'إنشاء ملفات Word'
                    })

                # استخدام النظام الجديد على مرحلتين
                result = process_contracts_two_phase(contracts_list, temp_dir, max_workers)

                if not result['success']:
                    with export_progress_lock:
                        export_progress[year].update({
                            'status': 'error',
                            'error': result.get('error', 'فشل في معالجة العقود')
                        })
                    return

                pdf_files = result['pdf_files']
                failed_contracts = result['failed_contracts']

                print(f"📊 النتائج النهائية:")
                print(f"   - تم إنشاء {result.get('word_created', 0)} ملف Word")
                print(f"   - تم تحويل {result.get('pdf_converted', 0)} ملف PDF")
                print(f"   - فشل في {len(failed_contracts)} عقد")

                # تحديث التقدم - بدء الدمج
                with export_progress_lock:
                    export_progress[year].update({
                        'status': 'merging',
                        'progress': 80,
                        'current_phase': 'دمج ملفات PDF',
                        'processed_contracts': len(pdf_files)
                    })
                # دمج جميع ملفات PDF في ملف واحد
                if pdf_files:
                    print(f"🔗 بدء دمج {len(pdf_files)} ملف PDF...")

                    # دمج محسن للأداء
                    final_pdf_path = os.path.join(temp_dir, f'contracts_export_{year}.pdf')
                    merger = PdfMerger()

                    # ترتيب ملفات PDF حسب الفهرس
                    pdf_files.sort(key=lambda x: int(os.path.basename(x).split('_')[2].split('.')[0]))

                    for pdf_file in pdf_files:
                        try:
                            merger.append(pdf_file)
                            print(f"✅ تم إضافة ملف PDF: {os.path.basename(pdf_file)}")
                        except Exception as e:
                            print(f"❌ خطأ في إضافة ملف PDF {pdf_file}: {e}")
                    # حفظ الملف المدموج
                    with open(final_pdf_path, 'wb') as output_file:
                        merger.write(output_file)
                    merger.close()

                    print(f"✅ تم دمج جميع العقود في ملف واحد: {final_pdf_path}")

                    # نسخ الملف إلى مجلد الرفع للوصول إليه
                    final_download_path = os.path.join(UPLOAD_FOLDER, f'contracts_export_{year}.pdf')
                    shutil.copy2(final_pdf_path, final_download_path)

                    # حفظ تقرير الأخطاء إذا كان هناك عقود فاشلة
                    error_report_path = None
                    if failed_contracts:
                        error_report_path = save_error_report(year, failed_contracts, total_contracts)
                        print(f"⚠️ تم إنشاء تقرير الأخطاء: {len(failed_contracts)} عقد فشل من أصل {total_contracts}")

                    with export_progress_lock:
                        export_progress[year].update({
                            'status': 'completed',
                            'progress': 100,
                            'download_path': f'contracts_export_{year}.pdf',
                            'file_size': os.path.getsize(final_download_path),
                            'failed_contracts_count': len(failed_contracts),
                            'success_rate': ((total_contracts - len(failed_contracts)) / total_contracts * 100) if total_contracts > 0 else 0,
                            'error_report_path': error_report_path
                        })

                else:
                    with export_progress_lock:
                        export_progress[year].update({
                            'status': 'error',
                            'error': 'فشل في إنشاء ملفات PDF للعقود'
                        })

            finally:
                # تنظيف الملفات المؤقتة
                try:
                    shutil.rmtree(temp_dir)
                except Exception as e:
                    print(f"خطأ في تنظيف الملفات المؤقتة: {e}")

        except Exception as e:
            with export_progress_lock:
                export_progress[year] = {
                    'status': 'error',
                    'error': f'خطأ في تصدير العقود: {str(e)}',
                    'progress': 0
                }

    # بدء المعالجة في خيط منفصل
    worker_thread = threading.Thread(target=export_worker, daemon=True)
    worker_thread.start()

    return {'success': True, 'message': 'تم بدء عملية التصدير', 'year': year}

# دالة مخصصة لإرسال الملف مع الحذف الفوري بعد الإرسال
def send_file_with_auto_cleanup(file_path, download_name, mimetype='application/pdf'):
    """
    إرسال ملف للمستخدم مع حذفه فوراً بعد الإرسال
    """
    try:
        # التحقق من وجود الملف
        if not os.path.exists(file_path):
            return {'error': 'الملف غير موجود'}, 404

        print(f"📤 إرسال الملف: {download_name}")

        # إرسال الملف
        response = send_file(
            file_path,
            as_attachment=True,
            download_name=download_name,
            mimetype=mimetype
        )

        # حذف فوري للملف بعد الإرسال مع تأخير بسيط فقط لضمان الإرسال
        def immediate_cleanup():
            time.sleep(3)  # تأخير بسيط جداً (3 ثواني) فقط لضمان اكتمال الإرسال
            remove_file_after_send(file_path)

        # جدولة الحذف في النظام المركزي
        def delayed_file_cleanup():
            time.sleep(5)  # انتظار 5 ثوانٍ قبل الحذف
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"🗑️ تم حذف الملف بعد الإرسال: {os.path.basename(file_path)}")
                schedule_cleanup_task('uploads_folder')
            except Exception as e:
                print(f"⚠️ خطأ في حذف الملف بعد الإرسال: {e}")

        # تشغيل الحذف المؤجل في خيط منفصل
        cleanup_thread = threading.Thread(target=delayed_file_cleanup, daemon=True)
        cleanup_thread.start()

        return response

    except Exception as e:
        print(f"❌ خطأ في إرسال الملف: {e}")
        return {'error': f'خطأ في إرسال الملف: {str(e)}'}, 500

def remove_file_after_send(file_path):
    """حذف الملف بعد إرساله للمستخدم"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"🗑️ تم حذف الملف بعد الإرسال: {os.path.basename(file_path)}")

            # حذف جميع الملفات المؤقتة في مجلد uploads
            cleanup_uploads_folder()

    except Exception as e:
        print(f"⚠️ خطأ في حذف الملف بعد الإرسال: {e}")

def immediate_cleanup_contract_files(computer_id, contract_id):
    """حذف فوري لجميع الملفات المؤقتة المتعلقة بالعقد"""
    try:
        print(f"🧹 حذف فوري لملفات العقد {contract_id} للمستخدم {computer_id}")

        if not os.path.exists(UPLOAD_FOLDER):
            return

        files_deleted = 0

        # أنماط الملفات المؤقتة للعقد الحالي
        contract_file_patterns = [
            f'computer_{computer_id}_*',
            f'buyer_{computer_id}*',
            f'seller_{computer_id}*',
            f'qr_{computer_id}*',
            f'temp_*_{computer_id}_*',
            f'*{contract_id}*'
        ]

        import glob
        for pattern in contract_file_patterns:
            pattern_path = os.path.join(UPLOAD_FOLDER, pattern)
            for file_path in glob.glob(pattern_path):
                try:
                    os.remove(file_path)
                    files_deleted += 1
                    print(f"🗑️ حذف فوري: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"⚠️ خطأ في الحذف الفوري {file_path}: {e}")

        if files_deleted > 0:
            print(f"✅ تم حذف {files_deleted} ملف فوراً للعقد {contract_id}")

    except Exception as e:
        print(f"⚠️ خطأ في الحذف الفوري للعقد {contract_id}: {e}")

def cleanup_user_files_after_contract(computer_id, contract_id):
    """حذف جميع الملفات المتعلقة بالمستخدم فوراً بعد إبرام العقد"""
    def immediate_user_cleanup():
        try:
            time.sleep(10)  # تأخير بسيط (10 ثواني) لضمان اكتمال العملية

            print(f"🧹 بدء تنظيف ملفات المستخدم {computer_id} للعقد {contract_id}")

            if not os.path.exists(UPLOAD_FOLDER):
                return

            files_deleted = 0

            # أنماط الملفات المتعلقة بالمستخدم والعقد
            user_file_patterns = [
                f'computer_{computer_id}_*',
                f'buyer_{computer_id}*',
                f'seller_{computer_id}*',
                f'contract_{computer_id}_*',
                f'*_{contract_id}_*',
                f'*{contract_id}*'
            ]

            import glob
            for pattern in user_file_patterns:
                pattern_path = os.path.join(UPLOAD_FOLDER, pattern)
                for file_path in glob.glob(pattern_path):
                    try:
                        # حذف الملف مباشرة بدون فحص العمر
                        os.remove(file_path)
                        files_deleted += 1
                        print(f"🗑️ تم حذف ملف المستخدم: {os.path.basename(file_path)}")
                    except Exception as e:
                        print(f"⚠️ خطأ في حذف ملف المستخدم {file_path}: {e}")

            if files_deleted > 0:
                print(f"✅ تم تنظيف {files_deleted} ملف للمستخدم {computer_id}")
            else:
                print(f"ℹ️ لا توجد ملفات للحذف للمستخدم {computer_id}")

        except Exception as e:
            print(f"⚠️ خطأ في تنظيف ملفات المستخدم {computer_id}: {e}")

    # جدولة التنظيف في النظام المركزي بدلاً من إنشاء خيط جديد
    schedule_cleanup_task('user_files', {'computer_id': computer_id})
    print(f"⏰ تم جدولة تنظيف ملفات المستخدم {computer_id} في النظام المركزي")

def _cleanup_user_files_internal(computer_id):
    """دالة داخلية لتنظيف ملفات المستخدم"""
    try:
        if not os.path.exists(UPLOAD_FOLDER):
            return

        user_file_patterns = [
            f'computer_{computer_id}_*',
            f'buyer_{computer_id}*',
            f'seller_{computer_id}*',
            f'temp_*_{computer_id}_*',
            f'preview_*_{computer_id}_*'
        ]

        import glob
        files_deleted = 0

        for pattern in user_file_patterns:
            pattern_path = os.path.join(UPLOAD_FOLDER, pattern)
            for file_path in glob.glob(pattern_path):
                try:
                    # حذف الملفات الأقدم من 10 ثوانٍ لضمان عدم التداخل
                    file_age = time.time() - os.path.getmtime(file_path)
                    if file_age > 10:
                        os.remove(file_path)
                        files_deleted += 1
                        print(f"🗑️ تم حذف ملف المستخدم: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف ملف المستخدم {file_path}: {e}")

        if files_deleted > 0:
            print(f"✅ تم تنظيف {files_deleted} ملف للمستخدم {computer_id}")

    except Exception as e:
        print(f"⚠️ خطأ في تنظيف ملفات المستخدم {computer_id}: {e}")

def _cleanup_uploads_folder_internal():
    """دالة داخلية لتنظيف مجلد uploads"""
    try:
        if not os.path.exists(UPLOAD_FOLDER):
            return

        # قائمة بأنواع الملفات المؤقتة التي يجب حذفها
        temp_file_patterns = [
            'temp_*.docx', 'temp_*.pdf', 'temp_*.png', 'temp_*.jpg',
            'preview_*.docx', 'preview_*.pdf', 'preview_*.png', 'preview_*.jpg',
            'export_*.docx', 'export_*.pdf', 'export_*.png', 'export_*.jpg',
            'seller_*.png', 'seller_*.jpg', 'buyer_*.png', 'buyer_*.jpg',
            'qr_*.png', 'qr_*.jpg', 'contract_*.docx', 'contract_*.pdf'
        ]

        import glob
        files_deleted = 0

        for pattern in temp_file_patterns:
            pattern_path = os.path.join(UPLOAD_FOLDER, pattern)
            for file_path in glob.glob(pattern_path):
                try:
                    # تجنب حذف الملفات الحديثة جداً (أقل من 30 ثانية)
                    file_age = time.time() - os.path.getmtime(file_path)
                    if file_age > 30:  # زيادة الوقت لتجنب التداخل
                        os.remove(file_path)
                        files_deleted += 1
                        print(f"🗑️ تم حذف الملف المؤقت: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف الملف المؤقت {file_path}: {e}")

        if files_deleted > 0:
            print(f"✅ تم تنظيف {files_deleted} ملف مؤقت من مجلد uploads")

    except Exception as e:
        print(f"⚠️ خطأ في تنظيف مجلد uploads: {e}")

def cleanup_uploads_folder():
    """تنظيف مجلد uploads من الملفات المؤقتة - واجهة عامة"""
    schedule_cleanup_task('uploads_folder')
    try:
        if not os.path.exists(UPLOAD_FOLDER):
            return

        # قائمة بأنواع الملفات المؤقتة التي يجب حذفها
        temp_file_patterns = [
            'temp_*.docx', 'temp_*.pdf', 'temp_*.png', 'temp_*.jpg',
            'preview_*.docx', 'preview_*.pdf', 'preview_*.png', 'preview_*.jpg',
            'export_*.docx', 'export_*.pdf', 'export_*.png', 'export_*.jpg',
            'seller_*.png', 'seller_*.jpg', 'buyer_*.png', 'buyer_*.jpg',
            'qr_*.png', 'qr_*.jpg', 'contract_*.docx', 'contract_*.pdf'
        ]

        import glob
        files_deleted = 0

        for pattern in temp_file_patterns:
            pattern_path = os.path.join(UPLOAD_FOLDER, pattern)
            for file_path in glob.glob(pattern_path):
                try:
                    # تجنب حذف الملفات الحديثة جداً (أقل من 5 ثواني) لضمان عدم التداخل مع العمليات الجارية
                    file_age = time.time() - os.path.getmtime(file_path)
                    if file_age > 5:  # 5 ثواني فقط
                        os.remove(file_path)
                        files_deleted += 1
                        print(f"🗑️ تم حذف الملف المؤقت: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف الملف المؤقت {file_path}: {e}")

        if files_deleted > 0:
            print(f"✅ تم تنظيف {files_deleted} ملف مؤقت من مجلد uploads")

    except Exception as e:
        print(f"⚠️ خطأ في تنظيف مجلد uploads: {e}")

# API لتحميل الملف المُصدر مع الحذف التلقائي
@app.route('/admin/api/download_exported_contracts/<int:year>')
def download_exported_contracts(year):
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    try:
        with export_progress_lock:
            progress_data = export_progress.get(year, {})

        if progress_data.get('status') != 'completed':
            return {'error': 'التصدير لم يكتمل بعد'}, 400

        download_path = progress_data.get('download_path')
        if not download_path:
            return {'error': 'مسار التحميل غير متوفر'}, 404

        file_path = os.path.join(UPLOAD_FOLDER, download_path)
        if not os.path.exists(file_path):
            return {'error': 'الملف غير موجود'}, 404

        # استخدام الدالة المخصصة للإرسال مع الحذف التلقائي
        return send_file_with_auto_cleanup(
            file_path,
            f'contracts_export_{year}.pdf',
            'application/pdf'
        )

    except Exception as e:
        return {'error': f'خطأ في تحميل الملف: {str(e)}'}, 500

# API لحذف عقود السنة بعد التحميل
@app.route('/admin/api/delete_year_contracts/<int:year>', methods=['POST'])
def delete_year_contracts_api(year):
    if not is_admin(session):
        return {'error': 'غير مصرح'}, 401

    try:
        data = request.json
        admin_password = data.get('admin_password', '')

        if not admin_password:
            return {'error': 'كلمة مرور الإدارة مطلوبة'}, 400

        success, message = delete_finalized_contracts_by_year(year, admin_password)

        if success:
            # حذف ملف التصدير إذا كان موجوداً
            try:
                with export_progress_lock:
                    progress_data = export_progress.get(year, {})
                    download_path = progress_data.get('download_path')
                    if download_path:
                        file_path = os.path.join(UPLOAD_FOLDER, download_path)
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    # حذف بيانات التقدم
                    if year in export_progress:
                        del export_progress[year]
            except Exception as e:
                print(f"خطأ في حذف ملف التصدير: {e}")

            return {'success': True, 'message': message}
        else:
            return {'error': message}, 400

    except Exception as e:
        return {'error': f'خطأ في حذف العقود: {str(e)}'}, 500

# ===== تصدير العقود إلى Excel =====

# API لتصدير العقود المبرمة إلى ملف Excel (للإداريين فقط)
@app.route('/api/export_contracts_excel')
def export_contracts_excel():
    # التحقق من صلاحيات الإدارة فقط
    if not is_admin(session):
        return {'error': 'غير مصرح - هذه الميزة متاحة للإداريين فقط'}, 401

    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from openpyxl.utils import get_column_letter
        import tempfile
        from datetime import datetime

        # جلب جميع العقود المبرمة
        from firebase_manager import firebase_manager
        all_contracts = firebase_manager.get_data("finalized_contracts")

        if not all_contracts:
            return {'error': 'لا توجد عقود مبرمة'}, 404

        # تحويل البيانات لقائمة مرتبة
        contracts_list = []
        for contract_id, contract_data in all_contracts.items():
            contract_info = contract_data.get('contract_data', {})

            # حساب المبلغ المتبقي إذا لم يكن موجوداً
            sale_amount = contract_info.get('badal_num', '')
            received_amount = contract_info.get('mony_num', '')
            remaining_amount = contract_info.get('mony_not_delevired', '')

            # إذا لم يكن المبلغ المتبقي محفوظاً، احسبه
            if not remaining_amount and sale_amount and received_amount:
                try:
                    sale_val = int(str(sale_amount).replace(',', '')) if sale_amount else 0
                    received_val = int(str(received_amount).replace(',', '')) if received_amount else 0
                    remaining_amount = str(sale_val - received_val)
                except (ValueError, TypeError):
                    remaining_amount = ''

            # دمج رقم السيارة مع المدينة
            car_number = contract_info.get('car_num', '')
            car_city = contract_info.get('car_city', '')

            # تكوين رقم السيارة مع المدينة
            if car_number and car_city:
                car_number_with_city = f"{car_city} {car_number}"
            elif car_number:
                car_number_with_city = car_number
            elif car_city:
                car_number_with_city = car_city
            else:
                car_number_with_city = ''

            contracts_list.append({
                'id': contract_id,
                'serial_number': contract_info.get('serial_number', ''),
                'date': contract_data.get('finalized_at', '').split(' ')[0] if contract_data.get('finalized_at') else '',
                'seller_name': contract_info.get('name_2', ''),
                'seller_phone': contract_info.get('phone_1', ''),  # phone_1 هو هاتف البائع
                'buyer_name': contract_info.get('name_3', ''),
                'buyer_phone': contract_info.get('phone_2', ''),   # phone_2 هو هاتف المشتري
                'car_type': contract_info.get('car_type', ''),
                'car_number_with_city': car_number_with_city,      # رقم السيارة مع المدينة
                'sale_amount': sale_amount,
                'currency_type': contract_info.get('currency_type', 'IQD'),
                'received_amount': received_amount,
                'remaining_amount': remaining_amount,
                'finalized_at': contract_data.get('finalized_at', '')
            })

        # ترتيب حسب التاريخ (الأحدث أولاً)
        contracts_list.sort(key=lambda x: x['finalized_at'], reverse=True)

        # إنشاء ملف Excel
        wb = Workbook()
        ws = wb.active
        ws.title = "العقود المبرمة"

        # تعيين اتجاه النص من اليمين لليسار
        ws.sheet_view.rightToLeft = True

        # تحديد العناوين
        headers = [
            'رقم العقد',
            'تاريخ العقد',
            'اسم البائع',
            'رقم تلفون البائع',
            'اسم المشتري',
            'رقم تلفون المشتري',
            'نوع السيارة',
            'رقم السيارة'
        ]

        # إضافة العناوين
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # إضافة البيانات
        for row, contract in enumerate(contracts_list, 2):
            data = [
                contract['serial_number'] or 'غير محدد',
                contract['date'] or 'غير محدد',
                contract['seller_name'] or 'غير محدد',
                contract['seller_phone'] or 'غير محدد',
                contract['buyer_name'] or 'غير محدد',
                contract['buyer_phone'] or 'غير محدد',
                contract['car_type'] or 'غير محدد',
                contract['car_number_with_city'] or 'غير محدد'  # رقم السيارة مع المدينة
            ]

            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.alignment = Alignment(horizontal="center", vertical="center")
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # تلوين الصفوف بالتناوب
                if row % 2 == 0:
                    cell.fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")

        # تعديل عرض الأعمدة
        column_widths = [12, 15, 20, 18, 20, 18, 15, 15]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col)].width = width

        # إضافة معلومات إضافية في أعلى الملف
        ws.insert_rows(1, 3)

        # عنوان الملف
        title_cell = ws.cell(row=1, column=1, value="تقرير العقود المبرمة")
        title_cell.font = Font(bold=True, size=16, color="366092")
        title_cell.alignment = Alignment(horizontal="center")
        ws.merge_cells(f'A1:{get_column_letter(len(headers))}1')

        # تاريخ التصدير
        date_cell = ws.cell(row=2, column=1, value=f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        date_cell.font = Font(size=12)
        date_cell.alignment = Alignment(horizontal="center")
        ws.merge_cells(f'A2:{get_column_letter(len(headers))}2')

        # عدد العقود
        count_cell = ws.cell(row=3, column=1, value=f"إجمالي العقود: {len(contracts_list)}")
        count_cell.font = Font(size=12, bold=True)
        count_cell.alignment = Alignment(horizontal="center")
        ws.merge_cells(f'A3:{get_column_letter(len(headers))}3')

        # حفظ الملف في مجلد مؤقت
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'contracts_export_{timestamp}.xlsx'
        filepath = os.path.join(UPLOAD_FOLDER, filename)

        wb.save(filepath)

        # إرسال الملف مع الحذف التلقائي
        return send_file_with_auto_cleanup(
            filepath,
            f'العقود_المبرمة_{timestamp}.xlsx',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except ImportError:
        return {'error': 'مكتبة openpyxl غير مثبتة. يرجى تثبيتها لتصدير Excel'}, 500
    except Exception as e:
        return {'error': f'خطأ في تصدير Excel: {str(e)}'}, 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 21717))
    print("✅ تم تهيئة Firebase بنجاح مع تحسينات الأداء")
    print(f"🚀 بدء تشغيل الخادم على المنفذ {port}")
    print("📋 نظام الرقم التسلسلي محسن لتوفير موارد السيرفر")
    print("🔄 نظام Gradio Client سيتم تحميله عند الحاجة فقط")
    print("⚡ النظام محسن لاستهلاك أقل للموارد عند عدم وجود طلبات")

    # إزالة فحص Gradio Space من هنا
    # لا نريد فحص تلقائي عند بدء التشغيل

    # ترحيل الصلاحيات للحاسبات الموجودة
    migrate_permissions()

    # تنظيف الصلاحيات القديمة غير المستخدمة
    cleanup_old_permissions()

    print("💡 Gradio Client سيتم تحميله عند أول طلب تحويل")
    print("🧹 نظام تنظيف الذاكرة الدوري سيبدأ بعد 5 دقائق من عدم النشاط")
    print("✅ جميع الأنظمة جاهزة مع تحسين الموارد!")

    # العمال سيبدؤون تلقائياً عند الحاجة فقط لتوفير موارد السيرفر
    print("💡 العمال سيبدؤون عند الحاجة فقط لتوفير موارد السيرفر")

    # بدء مراقب الذاكرة الدوري
    start_memory_monitor()

    app.run(host='0.0.0.0', port=port, debug=False)
